#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unified Language Detector for Deep Research Core.

Module này kết hợp tất cả tính năng tốt nhất từ các language detector implementations:
- LanguageDetector (deepresearch_organized & advanced_reasoning_engine): Multi-method detection
- SimpleLanguageDetector: Lightweight, no external dependencies
- AdvancedLanguageDetector: Caching, pattern matching, vocabulary detection

Tính năng chính:
- Multi-method language detection (chars, words, stopwords, external libs)
- Intelligent caching system
- Fallback mechanisms
- Confidence scoring
- Vietnamese-specific optimizations
- Extensible architecture
"""

import re
import os
import string
import hashlib
import logging
import pickle
from typing import Dict, Any, List, Optional, Union, Set, Tuple
from collections import Counter, defaultdict

# Thiết lập logging
logger = logging.getLogger(__name__)

# Kiểm tra các thư viện tùy chọn
try:
    from langdetect import detect, detect_langs, DetectorFactory
    from langdetect.lang_detect_exception import LangDetectException
    LANGDETECT_AVAILABLE = True
    # Đặt seed cho kết quả nhất quán
    DetectorFactory.seed = 0
except ImportError:
    LANGDETECT_AVAILABLE = False
    logger.debug("langdetect not available")

try:
    import langid
    LANGID_AVAILABLE = True
except ImportError:
    LANGID_AVAILABLE = False
    logger.debug("langid not available")

try:
    import fasttext
    FASTTEXT_AVAILABLE = True
except ImportError:
    FASTTEXT_AVAILABLE = False
    logger.debug("fasttext not available")


class LanguageDetectionCache:
    """Cache system cho language detection để tăng performance."""
    
    def __init__(self, cache_dir: Optional[str] = None, max_cache_size: int = 10000):
        """
        Khởi tạo cache.
        
        Args:
            cache_dir: Thư mục lưu cache (None = memory only)
            max_cache_size: Kích thước tối đa cache
        """
        self.cache_dir = cache_dir
        self.max_cache_size = max_cache_size
        self.memory_cache = {}
        self.cache_file = None
        
        if cache_dir:
            os.makedirs(cache_dir, exist_ok=True)
            self.cache_file = os.path.join(cache_dir, 'unified_language_cache.pkl')
            self._load_cache()
    
    def _load_cache(self):
        """Tải cache từ file."""
        if self.cache_file and os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'rb') as f:
                    self.memory_cache = pickle.load(f)
                logger.info(f"Loaded language cache: {len(self.memory_cache)} entries")
            except Exception as e:
                logger.warning(f"Failed to load cache: {e}")
                self.memory_cache = {}
    
    def _save_cache(self):
        """Lưu cache vào file."""
        if self.cache_file:
            try:
                with open(self.cache_file, 'wb') as f:
                    pickle.dump(self.memory_cache, f)
                logger.debug(f"Saved language cache: {len(self.memory_cache)} entries")
            except Exception as e:
                logger.warning(f"Failed to save cache: {e}")
    
    def get(self, text: str) -> Optional[Dict[str, Any]]:
        """Lấy kết quả từ cache."""
        key = self._create_key(text)
        return self.memory_cache.get(key)
    
    def set(self, text: str, result: Dict[str, Any]):
        """Lưu kết quả vào cache."""
        key = self._create_key(text)
        self.memory_cache[key] = result
        
        # Evict old entries if cache is full
        if len(self.memory_cache) > self.max_cache_size:
            items_to_remove = int(self.max_cache_size * 0.1)
            keys_to_remove = list(self.memory_cache.keys())[:items_to_remove]
            for k in keys_to_remove:
                del self.memory_cache[k]
        
        # Periodic save
        if self.cache_dir and len(self.memory_cache) % 100 == 0:
            self._save_cache()
    
    def _create_key(self, text: str) -> str:
        """Tạo cache key từ text."""
        if len(text) > 1000:
            # Sample long text
            sample = text[:300] + text[len(text)//2-150:len(text)//2+150] + text[-300:]
            return hashlib.md5(sample.encode('utf-8')).hexdigest()
        return hashlib.md5(text.encode('utf-8')).hexdigest()


class UnifiedLanguageDetector:
    """
    Unified language detector kết hợp tất cả tính năng tốt nhất.
    
    Tính năng:
    - Multi-method detection (chars, words, stopwords, external libs)
    - Intelligent caching
    - Confidence scoring
    - Vietnamese optimizations
    - Fallback mechanisms
    """
    
    def __init__(
        self,
        # Basic settings
        default_language: str = "en",
        min_text_length: int = 10,
        min_confidence: float = 0.5,
        
        # Supported languages
        supported_languages: Optional[List[str]] = None,
        
        # External libraries
        use_external_libraries: bool = True,
        fasttext_model_path: Optional[str] = None,
        
        # Caching
        enable_cache: bool = True,
        cache_dir: Optional[str] = None,
        max_cache_size: int = 10000,
        
        # Resources
        resources_dir: Optional[str] = None
    ):
        """
        Khởi tạo UnifiedLanguageDetector.
        
        Args:
            default_language: Ngôn ngữ mặc định
            min_text_length: Độ dài tối thiểu text để detect
            min_confidence: Confidence tối thiểu
            supported_languages: Danh sách ngôn ngữ hỗ trợ
            use_external_libraries: Sử dụng thư viện ngoài
            fasttext_model_path: Đường dẫn model fasttext
            enable_cache: Bật cache
            cache_dir: Thư mục cache
            max_cache_size: Kích thước cache tối đa
            resources_dir: Thư mục resources
        """
        # Basic settings
        self.default_language = default_language
        self.min_text_length = min_text_length
        self.min_confidence = min_confidence
        
        # Supported languages
        self.supported_languages = supported_languages or [
            "en", "vi", "zh", "ja", "ko", "fr", "de", "es", "it", "ru", 
            "ar", "hi", "th", "id", "ms", "pt", "nl", "pl", "tr", "sv"
        ]
        
        # Language names
        self.language_names = {
            "en": "English", "vi": "Vietnamese", "zh": "Chinese", "ja": "Japanese",
            "ko": "Korean", "fr": "French", "de": "German", "es": "Spanish",
            "it": "Italian", "ru": "Russian", "ar": "Arabic", "hi": "Hindi",
            "th": "Thai", "id": "Indonesian", "ms": "Malay", "pt": "Portuguese",
            "nl": "Dutch", "pl": "Polish", "tr": "Turkish", "sv": "Swedish"
        }
        
        # Character patterns for each language (use ranges for large sets)
        self.language_chars = {
            "vi": set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ"),
            "zh": (0x4E00, 0x9FFF),  # CJK Unified Ideographs - use range tuple
            "ja": [(0x3040, 0x309F), (0x30A0, 0x30FF)],  # Hiragana + Katakana - use range list
            "ko": (0xAC00, 0xD7A3),  # Hangul Syllables - use range tuple
            "th": (0x0E00, 0x0E7F),  # Thai - use range tuple
            "ar": [(0x0600, 0x06FF), (0x0750, 0x077F)],  # Arabic - use range list
            "hi": (0x0900, 0x097F),  # Devanagari - use range tuple
            "ru": (0x0400, 0x04FF),  # Cyrillic - use range tuple
            "fr": set("éèêëàâäôöùûüÿçœæÉÈÊËÀÂÄÔÖÙÛÜŸÇŒÆ"),
            "de": set("äöüßÄÖÜ"),
            "es": set("áéíóúüñ¿¡ÁÉÍÓÚÜÑ"),
            "it": set("àèéìíîòóùÀÈÉÌÍÎÒÓÙ"),
            "pt": set("áàâãéêíóôõúçÁÀÂÃÉÊÍÓÔÕÚÇ"),
            "pl": set("ąćęłńóśźżĄĆĘŁŃÓŚŹŻ"),
            "tr": set("çğıöşüÇĞIÖŞÜ"),
            "sv": set("åäöÅÄÖ")
        }
        
        # Common words for each language
        self.common_words = {
            "en": ["the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"],
            "vi": ["và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"],
            "fr": ["le", "la", "les", "un", "une", "des", "et", "est", "en", "à", "que", "qui", "dans", "pour", "pas"],
            "de": ["der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "auf", "für", "von", "nicht", "ein", "eine"],
            "es": ["el", "la", "los", "las", "un", "una", "unos", "unas", "y", "es", "en", "de", "que", "por", "para"],
            "it": ["il", "la", "i", "le", "un", "una", "e", "è", "in", "di", "che", "per", "non", "con", "sono"],
            "pt": ["o", "a", "os", "as", "um", "uma", "e", "é", "em", "de", "que", "para", "não", "com", "são"],
            "ru": ["и", "в", "не", "на", "я", "быть", "он", "с", "что", "а", "по", "это", "она", "этот", "к"],
            "zh": ["的", "一", "是", "在", "不", "了", "有", "和", "人", "这", "中", "大", "为", "上", "个"],
            "ja": ["の", "に", "は", "を", "た", "が", "で", "て", "と", "し", "れ", "さ", "ある", "いる", "も"],
            "ko": ["이", "그", "에", "의", "는", "과", "로", "을", "수", "있", "하", "것", "들", "그리고", "또한"],
            "id": ["yang", "dan", "di", "itu", "dengan", "untuk", "tidak", "akan", "pada", "dari", "juga", "saya", "ke", "ini", "ada"],
            "ms": ["yang", "dan", "di", "itu", "dengan", "untuk", "tidak", "akan", "pada", "dari", "juga", "saya", "ke", "ini", "ada"],
            "th": ["ที่", "และ", "ใน", "เป็น", "ของ", "มี", "จะ", "ได้", "แล้ว", "ไม่", "ก็", "ให้", "กับ", "ถ้า", "หรือ"],
            "ar": ["في", "من", "إلى", "على", "أن", "هذا", "هذه", "التي", "الذي", "ما", "لا", "كان", "قد", "عن", "مع"],
            "hi": ["और", "में", "की", "के", "को", "से", "पर", "है", "का", "एक", "यह", "होने", "था", "लिए", "तक"],
            "pl": ["i", "w", "na", "z", "do", "o", "że", "się", "nie", "to", "a", "od", "po", "za", "przez"],
            "nl": ["de", "het", "een", "en", "van", "in", "op", "dat", "met", "voor", "te", "aan", "is", "zijn", "er"],
            "tr": ["ve", "bir", "bu", "da", "de", "ile", "için", "o", "olan", "var", "daha", "çok", "gibi", "kadar", "sonra"],
            "sv": ["och", "i", "att", "det", "som", "på", "de", "av", "för", "är", "den", "till", "en", "om", "har"]
        }
        
        # Initialize cache
        self.cache = None
        if enable_cache:
            self.cache = LanguageDetectionCache(cache_dir, max_cache_size)
        
        # Initialize external libraries
        self.use_external_libraries = use_external_libraries
        self.langdetect_available = LANGDETECT_AVAILABLE and use_external_libraries
        self.langid_available = LANGID_AVAILABLE and use_external_libraries
        
        # Initialize fasttext
        self.fasttext_model = None
        self.fasttext_available = False
        if FASTTEXT_AVAILABLE and use_external_libraries and fasttext_model_path:
            try:
                if os.path.exists(fasttext_model_path):
                    self.fasttext_model = fasttext.load_model(fasttext_model_path)
                    self.fasttext_available = True
                    logger.info(f"Loaded fasttext model: {fasttext_model_path}")
            except Exception as e:
                logger.warning(f"Failed to load fasttext model: {e}")
        
        # Load stopwords
        self.resources_dir = resources_dir or os.path.join(os.path.dirname(__file__), "resources")
        self.stopwords = self._load_stopwords()
        
        logger.info(f"UnifiedLanguageDetector initialized with {len(self.supported_languages)} languages")
    
    def _load_stopwords(self) -> Dict[str, Set[str]]:
        """Tải stopwords cho mỗi ngôn ngữ."""
        stopwords = {}

        # Use built-in stopwords if files don't exist
        builtin_stopwords = {
            "en": {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"},
            "vi": {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"},
            "fr": {"le", "la", "les", "un", "une", "des", "et", "est", "en", "à", "que", "qui", "dans", "pour", "pas"},
            "de": {"der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "auf", "für", "von", "nicht", "ein", "eine"},
            "es": {"el", "la", "los", "las", "un", "una", "y", "es", "en", "de", "que", "por", "para", "con", "no"},
            "it": {"il", "la", "i", "le", "un", "una", "e", "è", "in", "di", "che", "per", "non", "con", "sono"},
            "pt": {"o", "a", "os", "as", "um", "uma", "e", "é", "em", "de", "que", "para", "não", "com", "são"},
            "ru": {"и", "в", "не", "на", "я", "быть", "он", "с", "что", "а", "по", "это", "она", "этот", "к"}
        }

        for lang in self.supported_languages:
            # Try to load from file first
            if self.resources_dir:
                stopwords_file = os.path.join(self.resources_dir, "stopwords", f"{lang}.txt")
                if os.path.exists(stopwords_file):
                    try:
                        with open(stopwords_file, "r", encoding="utf-8") as f:
                            stopwords[lang] = set(line.strip() for line in f if line.strip())
                        continue
                    except Exception as e:
                        logger.debug(f"Could not load stopwords for {lang}: {e}")

            # Use built-in stopwords as fallback
            stopwords[lang] = builtin_stopwords.get(lang, set())

        return stopwords

    def detect_language(self, text: str, return_confidence: bool = True) -> Union[str, Tuple[str, float], Dict[str, Any]]:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ
            return_confidence: Trả về confidence score

        Returns:
            str hoặc Tuple[str, float] hoặc Dict: Kết quả phát hiện ngôn ngữ
        """
        # Kiểm tra cache trước
        if self.cache:
            cached_result = self.cache.get(text)
            if cached_result:
                if return_confidence:
                    return cached_result["language"], cached_result["confidence"]
                return cached_result["language"]

        # Kiểm tra độ dài text
        if not text or len(text.strip()) < self.min_text_length:
            result = {
                "language": self.default_language,
                "confidence": 0.5,
                "method": "default_fallback",
                "languages": {self.default_language: 0.5}
            }
            if return_confidence:
                return result["language"], result["confidence"]
            return result["language"]

        # Chuẩn bị text
        text = text.strip()

        # Thu thập kết quả từ các phương pháp khác nhau
        methods_results = []

        # Method 1: Character-based detection (higher weight for languages with special chars)
        char_scores = self._detect_by_chars(text)
        if char_scores:
            # Give higher weight to character detection for non-Latin scripts
            char_weight = 0.6 if any(score > 0.1 for lang, score in char_scores.items()
                                   if lang in ["vi", "zh", "ja", "ko", "th", "ar", "hi", "ru"]) else 0.3
            methods_results.append(("chars", char_scores, char_weight))

        # Method 2: Common words detection
        word_scores = self._detect_by_common_words(text)
        if word_scores:
            methods_results.append(("words", word_scores, 0.4))

        # Method 3: Stopwords detection
        stopword_scores = self._detect_by_stopwords(text)
        if stopword_scores:
            methods_results.append(("stopwords", stopword_scores, 0.3))

        # Method 4: langdetect
        if self.langdetect_available:
            langdetect_scores = self._detect_with_langdetect(text)
            if langdetect_scores:
                methods_results.append(("langdetect", langdetect_scores, 0.7))

        # Method 5: langid
        if self.langid_available:
            langid_scores = self._detect_with_langid(text)
            if langid_scores:
                methods_results.append(("langid", langid_scores, 0.5))

        # Method 6: fasttext
        if self.fasttext_available:
            fasttext_scores = self._detect_with_fasttext(text)
            if fasttext_scores:
                methods_results.append(("fasttext", fasttext_scores, 0.8))

        # Combine results
        combined_result = self._combine_results(methods_results)

        # Cache result
        if self.cache:
            self.cache.set(text, combined_result)

        if return_confidence:
            return combined_result["language"], combined_result["confidence"]
        return combined_result["language"]

    def _detect_by_chars(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ dựa trên ký tự đặc trưng."""
        scores = {}
        text_lower = text.lower()

        for lang, chars in self.language_chars.items():
            if lang not in self.supported_languages:
                continue

            count = 0
            total = 0

            for char in text_lower:
                if not char.isspace() and char not in string.punctuation:
                    total += 1
                    char_code = ord(char)

                    if isinstance(chars, set):
                        # Set-based detection (for small character sets)
                        if char in chars or char_code in chars:
                            count += 1
                    elif isinstance(chars, tuple):
                        # Range tuple detection (start, end)
                        if chars[0] <= char_code <= chars[1]:
                            count += 1
                    elif isinstance(chars, list):
                        # Multiple ranges detection
                        for range_tuple in chars:
                            if range_tuple[0] <= char_code <= range_tuple[1]:
                                count += 1
                                break

            if total > 0:
                scores[lang] = count / total
            else:
                scores[lang] = 0.0

        # Special handling for English and Latin-based languages
        if all(score < 0.1 for score in scores.values()):
            english_chars = set("abcdefghijklmnopqrstuvwxyz")
            english_count = sum(1 for char in text_lower if char in english_chars)
            total_chars = sum(1 for char in text_lower
                            if not char.isspace() and char not in string.punctuation)

            if total_chars > 0 and english_count / total_chars > 0.7:
                scores["en"] = 0.8

        return scores

    def _detect_by_common_words(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ dựa trên từ phổ biến."""
        scores = {}
        text_lower = text.lower()

        # Extract words
        words = re.findall(r'\b\w+\b', text_lower)
        if not words:
            return {}

        for lang, common_words in self.common_words.items():
            if lang not in self.supported_languages:
                continue

            count = sum(1 for word in words if word in common_words)
            if len(words) > 0:
                scores[lang] = count / len(words)
            else:
                scores[lang] = 0.0

        return scores

    def _detect_by_stopwords(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ dựa trên stopwords."""
        scores = {}
        text_lower = text.lower()

        # Extract words
        words = re.findall(r'\b\w+\b', text_lower)
        if not words:
            return {}

        for lang, stopwords in self.stopwords.items():
            if lang not in self.supported_languages or not stopwords:
                continue

            count = sum(1 for word in words if word in stopwords)
            if len(words) > 0:
                scores[lang] = count / len(words)
            else:
                scores[lang] = 0.0

        return scores

    def _detect_with_langdetect(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ bằng langdetect."""
        try:
            langs = detect_langs(text)
            scores = {}
            for lang_obj in langs:
                lang = self._normalize_language_code(lang_obj.lang)
                if lang in self.supported_languages:
                    scores[lang] = lang_obj.prob
            return scores
        except Exception as e:
            logger.debug(f"langdetect error: {e}")
            return {}

    def _detect_with_langid(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ bằng langid."""
        try:
            lang, confidence = langid.classify(text)
            lang = self._normalize_language_code(lang)
            if lang in self.supported_languages:
                return {lang: confidence}
            return {}
        except Exception as e:
            logger.debug(f"langid error: {e}")
            return {}

    def _detect_with_fasttext(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ bằng fasttext."""
        try:
            predictions = self.fasttext_model.predict(text, k=5)
            scores = {}
            for i, label in enumerate(predictions[0]):
                lang = label.replace('__label__', '')
                lang = self._normalize_language_code(lang)
                if lang in self.supported_languages:
                    scores[lang] = float(predictions[1][i])
            return scores
        except Exception as e:
            logger.debug(f"fasttext error: {e}")
            return {}

    def _normalize_language_code(self, lang_code: str) -> str:
        """Chuẩn hóa mã ngôn ngữ."""
        code_mapping = {
            'zh-cn': 'zh', 'zh-tw': 'zh', 'zh_cn': 'zh', 'zh_tw': 'zh',
            'jpn': 'ja', 'kor': 'ko', 'vie': 'vi', 'eng': 'en',
            'fre': 'fr', 'fra': 'fr', 'deu': 'de', 'ger': 'de',
            'spa': 'es', 'rus': 'ru', 'ara': 'ar', 'tha': 'th',
            'por': 'pt', 'dut': 'nl', 'nld': 'nl', 'pol': 'pl',
            'tur': 'tr', 'swe': 'sv'
        }

        lang_code = lang_code.lower()
        return code_mapping.get(lang_code, lang_code)

    def _combine_results(self, methods_results: List[Tuple[str, Dict[str, float], float]]) -> Dict[str, Any]:
        """
        Kết hợp kết quả từ các phương pháp khác nhau.

        Args:
            methods_results: List of (method_name, scores, weight)

        Returns:
            Dict chứa kết quả kết hợp
        """
        if not methods_results:
            return {
                "language": self.default_language,
                "confidence": 0.5,
                "method": "fallback",
                "languages": {self.default_language: 0.5}
            }

        # Combine scores with weights
        combined_scores = defaultdict(float)
        total_weight = 0.0
        methods_used = []

        for method_name, scores, weight in methods_results:
            methods_used.append(method_name)
            total_weight += weight

            for lang, score in scores.items():
                if lang in self.supported_languages:
                    combined_scores[lang] += score * weight

        # Normalize scores
        if total_weight > 0:
            for lang in combined_scores:
                combined_scores[lang] /= total_weight

        # Find best language
        if combined_scores:
            sorted_langs = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            best_lang, best_score = sorted_langs[0]

            # Debug output
            logger.debug(f"Combined scores: {dict(combined_scores)}")
            logger.debug(f"Sorted langs: {sorted_langs[:3]}")
            logger.debug(f"Best: {best_lang} ({best_score:.3f})")

            # Special handling for Vietnamese and other non-Latin languages
            # If we detect special characters, lower the confidence threshold
            if best_lang in ["vi", "zh", "ja", "ko", "th", "ar", "hi", "ru"] and best_score > 0.1:
                # For languages with special characters, accept lower confidence
                logger.debug(f"Accepting special language {best_lang} with score {best_score:.3f}")
                pass  # Keep the detected language
            elif best_score < self.min_confidence:
                # Check if any non-Latin language has reasonable score
                for lang, score in sorted_langs:
                    if lang in ["vi", "zh", "ja", "ko", "th", "ar", "hi", "ru"] and score > 0.1:
                        logger.debug(f"Found special language {lang} with score {score:.3f}")
                        best_lang, best_score = lang, score
                        break
                else:
                    # No special language detected, use default
                    logger.debug(f"No special language found, using default {self.default_language}")
                    best_lang = self.default_language
                    best_score = 0.5
        else:
            best_lang, best_score = self.default_language, 0.5
            sorted_langs = [(best_lang, best_score)]

        return {
            "language": best_lang,
            "confidence": best_score,
            "method": "+".join(methods_used) if methods_used else "fallback",
            "languages": dict(sorted_langs[:5]),  # Top 5 languages
            "language_name": self.language_names.get(best_lang, best_lang)
        }

    # Utility methods for compatibility
    def detect(self, text: str) -> str:
        """Simple detection method for compatibility."""
        return self.detect_language(text, return_confidence=False)

    def is_language(self, text: str, language: str, min_confidence: float = None) -> bool:
        """
        Kiểm tra xem text có phải là ngôn ngữ cụ thể không.

        Args:
            text: Text cần kiểm tra
            language: Mã ngôn ngữ
            min_confidence: Confidence tối thiểu

        Returns:
            bool: True nếu là ngôn ngữ đó
        """
        if min_confidence is None:
            min_confidence = self.min_confidence

        lang, confidence = self.detect_language(text, return_confidence=True)
        return lang == language and confidence >= min_confidence

    def is_vietnamese(self, text: str, min_confidence: float = None) -> bool:
        """Kiểm tra xem text có phải tiếng Việt không."""
        return self.is_language(text, "vi", min_confidence)

    def is_english(self, text: str, min_confidence: float = None) -> bool:
        """Kiểm tra xem text có phải tiếng Anh không."""
        return self.is_language(text, "en", min_confidence)

    def get_supported_languages(self) -> Dict[str, str]:
        """Lấy danh sách ngôn ngữ được hỗ trợ."""
        return {lang: self.language_names.get(lang, lang) for lang in self.supported_languages}

    def augment_with_language_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Bổ sung thông tin ngôn ngữ vào data.

        Args:
            data: Data cần bổ sung thông tin ngôn ngữ

        Returns:
            Data đã được bổ sung thông tin ngôn ngữ
        """
        # Find text fields
        for key, value in data.items():
            if isinstance(value, str) and len(value) > self.min_text_length:
                lang, conf = self.detect_language(value, return_confidence=True)
                if conf > self.min_confidence:
                    if "metadata" not in data:
                        data["metadata"] = {}
                    if "language" not in data["metadata"]:
                        data["metadata"]["language"] = {}

                    data["metadata"]["language"][key] = {
                        "code": lang,
                        "confidence": conf,
                        "name": self.language_names.get(lang, lang)
                    }

        # Handle nested results
        if "results" in data and isinstance(data["results"], list):
            for i, result in enumerate(data["results"]):
                if isinstance(result, dict):
                    text_content = ""
                    for field in ["title", "snippet", "content", "text", "description"]:
                        if field in result and isinstance(result[field], str):
                            text_content += result[field] + " "

                    if len(text_content) > self.min_text_length:
                        lang, conf = self.detect_language(text_content, return_confidence=True)
                        if conf > self.min_confidence:
                            if "metadata" not in result:
                                result["metadata"] = {}

                            result["metadata"]["language"] = {
                                "code": lang,
                                "confidence": conf,
                                "name": self.language_names.get(lang, lang)
                            }

        return data

    def get_stats(self) -> Dict[str, Any]:
        """Lấy thống kê về detector."""
        stats = {
            "supported_languages": len(self.supported_languages),
            "default_language": self.default_language,
            "min_text_length": self.min_text_length,
            "min_confidence": self.min_confidence,
            "external_libraries": {
                "langdetect": self.langdetect_available,
                "langid": self.langid_available,
                "fasttext": self.fasttext_available
            }
        }

        if self.cache:
            stats["cache_size"] = len(self.cache.memory_cache)
            stats["cache_enabled"] = True
        else:
            stats["cache_enabled"] = False

        return stats


# Convenience functions for backward compatibility
def detect_language(text: str, config: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
    """
    Convenience function để phát hiện ngôn ngữ.

    Args:
        text: Text cần phát hiện ngôn ngữ
        config: Cấu hình detector

    Returns:
        Tuple[str, float]: (language_code, confidence)
    """
    detector = UnifiedLanguageDetector(**(config or {}))
    return detector.detect_language(text, return_confidence=True)


def is_vietnamese(text: str) -> bool:
    """Convenience function kiểm tra tiếng Việt."""
    detector = UnifiedLanguageDetector()
    return detector.is_vietnamese(text)


def is_english(text: str) -> bool:
    """Convenience function kiểm tra tiếng Anh."""
    detector = UnifiedLanguageDetector()
    return detector.is_english(text)


# Global detector instance
_global_detector_instance = None

def get_language_detector(**kwargs) -> UnifiedLanguageDetector:
    """
    Lấy global detector instance.

    Args:
        **kwargs: Các tham số cho detector

    Returns:
        UnifiedLanguageDetector instance
    """
    global _global_detector_instance
    if _global_detector_instance is None:
        _global_detector_instance = UnifiedLanguageDetector(**kwargs)
    return _global_detector_instance
