#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Language Detector Configuration - Production settings.

Cung cấp các configuration templates cho different environments và use cases.
"""

from typing import Dict, Any
from enum import Enum


class Environment(Enum):
    """Các môi trường deployment."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class PerformanceProfile(Enum):
    """Các profile performance."""
    HIGH_SPEED = "high_speed"      # Tối ưu cho speed
    HIGH_ACCURACY = "high_accuracy"  # Tối ưu cho accuracy
    BALANCED = "balanced"          # Cân bằng speed và accuracy
    LOW_MEMORY = "low_memory"      # Tối ưu cho memory usage


def get_config(
    environment: Environment = Environment.PRODUCTION,
    performance_profile: PerformanceProfile = PerformanceProfile.BALANCED
) -> Dict[str, Any]:
    """
    Lấy configuration cho environment và performance profile cụ thể.
    
    Args:
        environment: Môi trường deployment
        performance_profile: Profile performance
        
    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    
    # Base configuration
    base_config = {
        'default_language': 'en',
        'min_confidence': 0.3,
        'enable_logging': True,
        'enable_caching': True,
    }
    
    # Environment-specific settings
    env_configs = {
        Environment.DEVELOPMENT: {
            'enable_logging': True,
            'cache_size': 100,
            'log_level': 'DEBUG',
        },
        Environment.TESTING: {
            'enable_logging': False,
            'cache_size': 50,
            'enable_caching': False,  # Disable caching for consistent tests
        },
        Environment.STAGING: {
            'enable_logging': True,
            'cache_size': 500,
            'log_level': 'INFO',
        },
        Environment.PRODUCTION: {
            'enable_logging': True,
            'cache_size': 2000,
            'log_level': 'WARNING',
        }
    }
    
    # Performance profile settings
    profile_configs = {
        PerformanceProfile.HIGH_SPEED: {
            'cache_size': 5000,
            'min_confidence': 0.2,  # Lower threshold for faster decisions
            'enable_fallback': True,
        },
        PerformanceProfile.HIGH_ACCURACY: {
            'cache_size': 1000,
            'min_confidence': 0.5,  # Higher threshold for better accuracy
            'enable_fallback': True,
        },
        PerformanceProfile.BALANCED: {
            'cache_size': 2000,
            'min_confidence': 0.3,
            'enable_fallback': True,
        },
        PerformanceProfile.LOW_MEMORY: {
            'cache_size': 200,
            'enable_caching': True,
            'min_confidence': 0.3,
        }
    }
    
    # Merge configurations
    config = base_config.copy()
    config.update(env_configs.get(environment, {}))
    config.update(profile_configs.get(performance_profile, {}))
    
    return config


def get_optimized_detector_config(
    environment: Environment = Environment.PRODUCTION,
    performance_profile: PerformanceProfile = PerformanceProfile.BALANCED
) -> Dict[str, Any]:
    """
    Lấy configuration cho OptimizedLanguageDetector.
    
    Args:
        environment: Môi trường deployment
        performance_profile: Profile performance
        
    Returns:
        Dict[str, Any]: Configuration cho OptimizedLanguageDetector
    """
    return get_config(environment, performance_profile)


def get_manager_config(
    environment: Environment = Environment.PRODUCTION,
    performance_profile: PerformanceProfile = PerformanceProfile.BALANCED
) -> Dict[str, Any]:
    """
    Lấy configuration cho LanguageDetectorManager.
    
    Args:
        environment: Môi trường deployment
        performance_profile: Profile performance
        
    Returns:
        Dict[str, Any]: Configuration cho LanguageDetectorManager
    """
    base_config = get_config(environment, performance_profile)
    
    # Manager-specific settings
    manager_specific = {
        'default_strategy': 'auto',
        'enable_fallback': True,
    }
    
    # Performance profile adjustments for manager
    if performance_profile == PerformanceProfile.HIGH_SPEED:
        manager_specific['default_strategy'] = 'fast'
    elif performance_profile == PerformanceProfile.HIGH_ACCURACY:
        manager_specific['default_strategy'] = 'accurate'
    
    base_config.update(manager_specific)
    return base_config


# Predefined configurations
CONFIGS = {
    # Development configurations
    'dev_fast': get_config(Environment.DEVELOPMENT, PerformanceProfile.HIGH_SPEED),
    'dev_accurate': get_config(Environment.DEVELOPMENT, PerformanceProfile.HIGH_ACCURACY),
    'dev_balanced': get_config(Environment.DEVELOPMENT, PerformanceProfile.BALANCED),
    
    # Testing configurations
    'test_basic': get_config(Environment.TESTING, PerformanceProfile.BALANCED),
    'test_performance': get_config(Environment.TESTING, PerformanceProfile.HIGH_SPEED),
    
    # Production configurations
    'prod_fast': get_config(Environment.PRODUCTION, PerformanceProfile.HIGH_SPEED),
    'prod_accurate': get_config(Environment.PRODUCTION, PerformanceProfile.HIGH_ACCURACY),
    'prod_balanced': get_config(Environment.PRODUCTION, PerformanceProfile.BALANCED),
    'prod_low_memory': get_config(Environment.PRODUCTION, PerformanceProfile.LOW_MEMORY),
}


def get_predefined_config(config_name: str) -> Dict[str, Any]:
    """
    Lấy predefined configuration.
    
    Args:
        config_name: Tên configuration
        
    Returns:
        Dict[str, Any]: Configuration dictionary
        
    Raises:
        ValueError: Nếu config_name không tồn tại
    """
    if config_name not in CONFIGS:
        available_configs = list(CONFIGS.keys())
        raise ValueError(f"Unknown config '{config_name}'. Available: {available_configs}")
    
    return CONFIGS[config_name].copy()


def validate_config(config: Dict[str, Any]) -> bool:
    """
    Validate configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        bool: True nếu config hợp lệ
        
    Raises:
        ValueError: Nếu config không hợp lệ
    """
    required_keys = ['default_language', 'min_confidence']
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required config key: {key}")
    
    # Validate values
    if not isinstance(config['min_confidence'], (int, float)):
        raise ValueError("min_confidence must be a number")
    
    if not 0.0 <= config['min_confidence'] <= 1.0:
        raise ValueError("min_confidence must be between 0.0 and 1.0")
    
    if 'cache_size' in config:
        if not isinstance(config['cache_size'], int) or config['cache_size'] < 0:
            raise ValueError("cache_size must be a non-negative integer")
    
    return True


# Configuration examples for different use cases
USE_CASE_CONFIGS = {
    # Web application với high traffic
    'web_app_high_traffic': {
        'default_language': 'en',
        'min_confidence': 0.2,
        'enable_caching': True,
        'cache_size': 10000,
        'enable_logging': True,
        'log_level': 'WARNING',
        'default_strategy': 'fast',
    },
    
    # Content analysis với accuracy cao
    'content_analysis': {
        'default_language': 'en',
        'min_confidence': 0.6,
        'enable_caching': True,
        'cache_size': 2000,
        'enable_logging': True,
        'log_level': 'INFO',
        'default_strategy': 'accurate',
    },
    
    # Vietnamese-focused application
    'vietnamese_app': {
        'default_language': 'vi',
        'min_confidence': 0.3,
        'enable_caching': True,
        'cache_size': 3000,
        'enable_logging': True,
        'log_level': 'INFO',
        'default_strategy': 'vietnamese',
    },
    
    # Microservice với memory constraints
    'microservice_low_memory': {
        'default_language': 'en',
        'min_confidence': 0.3,
        'enable_caching': True,
        'cache_size': 500,
        'enable_logging': False,
        'default_strategy': 'fast',
    },
    
    # Batch processing
    'batch_processing': {
        'default_language': 'en',
        'min_confidence': 0.3,
        'enable_caching': False,  # Disable caching for batch processing
        'enable_logging': True,
        'log_level': 'ERROR',
        'default_strategy': 'balanced',
    },
}


def get_use_case_config(use_case: str) -> Dict[str, Any]:
    """
    Lấy configuration cho use case cụ thể.
    
    Args:
        use_case: Tên use case
        
    Returns:
        Dict[str, Any]: Configuration dictionary
        
    Raises:
        ValueError: Nếu use_case không tồn tại
    """
    if use_case not in USE_CASE_CONFIGS:
        available_use_cases = list(USE_CASE_CONFIGS.keys())
        raise ValueError(f"Unknown use case '{use_case}'. Available: {available_use_cases}")
    
    return USE_CASE_CONFIGS[use_case].copy()


def create_custom_config(
    base_config: str = 'prod_balanced',
    **overrides
) -> Dict[str, Any]:
    """
    Tạo custom configuration từ base config.
    
    Args:
        base_config: Tên base configuration
        **overrides: Các settings để override
        
    Returns:
        Dict[str, Any]: Custom configuration
    """
    config = get_predefined_config(base_config)
    config.update(overrides)
    validate_config(config)
    return config


# Logging configuration
LOGGING_CONFIGS = {
    'development': {
        'level': 'DEBUG',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'handlers': ['console', 'file']
    },
    'production': {
        'level': 'WARNING',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'handlers': ['file', 'syslog']
    },
    'testing': {
        'level': 'ERROR',
        'format': '%(levelname)s - %(message)s',
        'handlers': ['console']
    }
}


def get_logging_config(environment: str = 'production') -> Dict[str, Any]:
    """
    Lấy logging configuration.
    
    Args:
        environment: Môi trường (development, production, testing)
        
    Returns:
        Dict[str, Any]: Logging configuration
    """
    return LOGGING_CONFIGS.get(environment, LOGGING_CONFIGS['production']).copy()
