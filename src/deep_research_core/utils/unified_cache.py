#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unified Cache System for Deep Research Core.

Module này kết hợp tất cả tính năng tốt nhất từ các cache implementations khác nhau:
- AdvancedReActCache: Semantic caching, adaptive TTL, compression
- Cache (cache_utils): Simple TTL-based caching với persistence
- CacheManager: Multi-tier caching (memory + disk + Redis)
- QueryAnalyzerCache: Query-specific caching với similarity search

Tính năng chính:
- Multi-tier caching (Memory -> Disk -> Redis)
- Adaptive TTL based on content type và usage patterns
- Semantic similarity search
- Compression và optimization
- Thread-safe operations
- Comprehensive statistics
- Intelligent cache invalidation
"""

import os
import time
import json
import pickle
import gzip
import hashlib
import threading
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union, Tuple, Callable
from collections import OrderedDict
from datetime import datetime, timedelta
from functools import lru_cache

# Thiết lập logging
logger = logging.getLogger(__name__)

# <PERSON><PERSON><PERSON> tra các thư viện tùy chọn
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available. Redis cache will be disabled.")

try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available. Advanced similarity search will be disabled.")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("sentence-transformers not available. Semantic search will be disabled.")


class CacheEntry:
    """Đại diện cho một mục trong cache với metadata đầy đủ."""
    
    def __init__(
        self,
        value: Any,
        ttl: int = 3600,
        metadata: Optional[Dict[str, Any]] = None,
        content_type: str = "default",
        tool_name: Optional[str] = None,
        tool_args: Optional[Dict[str, Any]] = None
    ):
        """
        Khởi tạo cache entry.
        
        Args:
            value: Giá trị cần cache
            ttl: Thời gian sống (giây)
            metadata: Metadata bổ sung
            content_type: Loại nội dung (news, wiki, etc.)
            tool_name: Tên tool (cho ReAct caching)
            tool_args: Arguments của tool
        """
        self.value = value
        self.ttl = ttl
        self.created_at = time.time()
        self.last_accessed = time.time()
        self.access_count = 1
        self.metadata = metadata or {}
        self.content_type = content_type
        self.tool_name = tool_name
        self.tool_args = tool_args or {}
        
    def is_expired(self) -> bool:
        """Kiểm tra xem entry có hết hạn không."""
        return time.time() > (self.created_at + self.ttl)
    
    def get_remaining_ttl(self) -> float:
        """Lấy thời gian còn lại."""
        return max(0, (self.created_at + self.ttl) - time.time())
    
    def touch(self):
        """Cập nhật thời gian truy cập và số lần truy cập."""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def extend_ttl(self, additional_time: int):
        """Gia hạn TTL."""
        self.ttl += additional_time
    
    def update_value(self, value: Any):
        """Cập nhật giá trị."""
        self.value = value
        self.created_at = time.time()
        self.last_accessed = time.time()


class UnifiedCache:
    """
    Unified cache system kết hợp tất cả tính năng tốt nhất.
    
    Tính năng:
    - Multi-tier caching (Memory -> Disk -> Redis)
    - Adaptive TTL
    - Semantic similarity search
    - Compression
    - Thread-safe operations
    - Comprehensive statistics
    """
    
    def __init__(
        self,
        # Basic settings
        default_ttl: int = 3600,
        max_memory_size: int = 1000,
        max_disk_size: int = 10000,
        
        # Storage settings
        cache_dir: Optional[str] = None,
        enable_disk_cache: bool = True,
        enable_redis_cache: bool = False,
        redis_url: Optional[str] = None,
        
        # Advanced features
        enable_compression: bool = True,
        compression_level: int = 6,
        enable_semantic_search: bool = False,
        similarity_threshold: float = 0.8,
        adaptive_ttl: bool = True,
        
        # Performance settings
        cleanup_interval: int = 300,
        enable_statistics: bool = True,
        
        # Embedding model for semantic search
        embedding_model: str = "all-MiniLM-L6-v2"
    ):
        """
        Khởi tạo UnifiedCache.
        
        Args:
            default_ttl: TTL mặc định (giây)
            max_memory_size: Kích thước tối đa memory cache
            max_disk_size: Kích thước tối đa disk cache
            cache_dir: Thư mục cache
            enable_disk_cache: Bật disk cache
            enable_redis_cache: Bật Redis cache
            redis_url: URL Redis server
            enable_compression: Bật nén dữ liệu
            compression_level: Mức độ nén (1-9)
            enable_semantic_search: Bật tìm kiếm ngữ nghĩa
            similarity_threshold: Ngưỡng tương đồng
            adaptive_ttl: Bật TTL động
            cleanup_interval: Khoảng thời gian dọn dẹp (giây)
            enable_statistics: Bật thống kê
            embedding_model: Model embedding cho semantic search
        """
        # Basic settings
        self.default_ttl = default_ttl
        self.max_memory_size = max_memory_size
        self.max_disk_size = max_disk_size
        self.adaptive_ttl = adaptive_ttl
        self.cleanup_interval = cleanup_interval
        self.enable_statistics = enable_statistics
        
        # Storage settings
        self.enable_disk_cache = enable_disk_cache
        self.enable_redis_cache = enable_redis_cache and REDIS_AVAILABLE
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser("~"), ".deep_research_cache")
        
        # Compression settings
        self.enable_compression = enable_compression
        self.compression_level = compression_level
        
        # Semantic search settings
        self.enable_semantic_search = enable_semantic_search and (SKLEARN_AVAILABLE or SENTENCE_TRANSFORMERS_AVAILABLE)
        self.similarity_threshold = similarity_threshold
        
        # Initialize memory cache (LRU)
        self.memory_cache = OrderedDict()
        self.memory_lock = threading.RLock()
        
        # Initialize disk cache
        if self.enable_disk_cache:
            os.makedirs(self.cache_dir, exist_ok=True)
            self.disk_metadata_file = os.path.join(self.cache_dir, "metadata.json")
            self.disk_metadata = self._load_disk_metadata()
        
        # Initialize Redis cache
        self.redis_client = None
        if self.enable_redis_cache:
            try:
                self.redis_client = redis.from_url(redis_url or "redis://localhost:6379/0")
                self.redis_client.ping()  # Test connection
                logger.info("Redis cache initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Redis cache: {str(e)}")
                self.enable_redis_cache = False
        
        # Initialize semantic search
        self.embedding_model = None
        if self.enable_semantic_search:
            self._init_embedding_model(embedding_model)
        
        # Initialize statistics
        self.stats = {
            "memory_hits": 0,
            "disk_hits": 0,
            "redis_hits": 0,
            "semantic_hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "evictions": 0,
            "compressions": 0,
            "decompressions": 0
        }
        
        # Cleanup tracking
        self.last_cleanup = time.time()
        
        # Start background cleanup if needed
        self._maybe_cleanup()
        
        logger.info(f"UnifiedCache initialized with memory_size={max_memory_size}, "
                   f"disk_cache={enable_disk_cache}, redis_cache={enable_redis_cache}, "
                   f"semantic_search={self.enable_semantic_search}")
    
    def _init_embedding_model(self, model_name: str):
        """Khởi tạo embedding model cho semantic search."""
        try:
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                self.embedding_model = SentenceTransformer(model_name)
                logger.info(f"Initialized SentenceTransformer: {model_name}")
            elif SKLEARN_AVAILABLE:
                self.embedding_model = TfidfVectorizer(max_features=1000, stop_words="english")
                logger.info("Initialized TfidfVectorizer for semantic search")
            else:
                self.enable_semantic_search = False
                logger.warning("No embedding model available, semantic search disabled")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {str(e)}")
            self.enable_semantic_search = False
    
    def _load_disk_metadata(self) -> Dict[str, Any]:
        """Tải metadata từ disk."""
        try:
            if os.path.exists(self.disk_metadata_file):
                with open(self.disk_metadata_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load disk metadata: {str(e)}")
        
        return {
            "entries": {},
            "stats": {"hits": 0, "misses": 0, "sets": 0, "deletes": 0},
            "last_cleanup": time.time()
        }
    
    def _save_disk_metadata(self):
        """Lưu metadata xuống disk."""
        if not self.enable_disk_cache:
            return

        try:
            with open(self.disk_metadata_file, "w", encoding="utf-8") as f:
                json.dump(self.disk_metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save disk metadata: {str(e)}")

    def _generate_cache_key(self, key: str, tool_name: Optional[str] = None, tool_args: Optional[Dict[str, Any]] = None) -> str:
        """
        Tạo cache key từ input parameters.

        Args:
            key: Key cơ bản
            tool_name: Tên tool (cho ReAct caching)
            tool_args: Arguments của tool

        Returns:
            Cache key đã được hash
        """
        if tool_name and tool_args:
            # ReAct-style caching
            args_str = json.dumps(tool_args, sort_keys=True)
            combined_key = f"{tool_name}:{args_str}:{key}"
        else:
            combined_key = key

        # Tạo hash để có key ngắn gọn và consistent
        return hashlib.md5(combined_key.encode('utf-8')).hexdigest()

    def _calculate_adaptive_ttl(self, key: str, content_type: str = "default", access_count: int = 1) -> int:
        """
        Tính toán TTL động dựa trên content type và usage patterns.

        Args:
            key: Cache key
            content_type: Loại nội dung
            access_count: Số lần truy cập

        Returns:
            TTL được điều chỉnh (giây)
        """
        if not self.adaptive_ttl:
            return self.default_ttl

        # Base TTL
        base_ttl = self.default_ttl

        # TTL factors cho các loại nội dung khác nhau
        ttl_factors = {
            "news": 0.5,        # Tin tức thay đổi nhanh
            "wiki": 2.0,        # Wiki ít thay đổi hơn
            "forum": 1.0,       # Forum thay đổi trung bình
            "blog": 1.5,        # Blog thay đổi chậm hơn
            "social": 0.3,      # Mạng xã hội thay đổi rất nhanh
            "academic": 3.0,    # Nội dung học thuật thay đổi rất chậm
            "video": 2.0,       # Video ít thay đổi
            "product": 0.7,     # Thông tin sản phẩm thay đổi khá nhanh
            "weather": 0.1,     # Thời tiết thay đổi rất nhanh
            "stock": 0.05,      # Chứng khoán thay đổi cực nhanh
            "search": 0.8,      # Kết quả tìm kiếm thay đổi khá nhanh
            "static": 1.5,      # Dữ liệu tĩnh ít thay đổi
            "config": 2.0,      # Config ít thay đổi
            "default": 1.0      # Mặc định
        }

        # Áp dụng factor cho content type
        content_factor = ttl_factors.get(content_type.lower(), 1.0)
        base_ttl *= content_factor

        # Điều chỉnh dựa trên access count
        if access_count > 10:
            # Frequently accessed items get longer TTL
            base_ttl *= 2.0
        elif access_count > 5:
            base_ttl *= 1.5
        elif access_count < 2:
            # Rarely accessed items get shorter TTL
            base_ttl *= 0.5

        # Giới hạn trong phạm vi hợp lý
        min_ttl = 300  # Ít nhất 5 phút
        max_ttl = 86400 * 7  # Tối đa 1 tuần

        return int(max(min_ttl, min(max_ttl, base_ttl)))

    def _compress_data(self, data: bytes) -> Tuple[bytes, bool]:
        """
        Nén dữ liệu nếu có lợi.

        Args:
            data: Dữ liệu cần nén

        Returns:
            Tuple (compressed_data, was_compressed)
        """
        if not self.enable_compression:
            return data, False

        try:
            compressed = gzip.compress(data, compresslevel=self.compression_level)
            # Chỉ sử dụng nén nếu kích thước giảm đáng kể
            if len(compressed) < len(data) * 0.9:  # Giảm ít nhất 10%
                if self.enable_statistics:
                    self.stats["compressions"] += 1
                return compressed, True
            else:
                return data, False
        except Exception as e:
            logger.error(f"Compression failed: {str(e)}")
            return data, False

    def _decompress_data(self, data: bytes, was_compressed: bool) -> bytes:
        """
        Giải nén dữ liệu nếu cần.

        Args:
            data: Dữ liệu cần giải nén
            was_compressed: Có được nén không

        Returns:
            Dữ liệu đã giải nén
        """
        if not was_compressed:
            return data

        try:
            if self.enable_statistics:
                self.stats["decompressions"] += 1
            return gzip.decompress(data)
        except Exception as e:
            logger.error(f"Decompression failed: {str(e)}")
            return data

    def _get_disk_file_path(self, cache_key: str) -> str:
        """
        Lấy đường dẫn file cho cache key.

        Args:
            cache_key: Cache key

        Returns:
            Đường dẫn file
        """
        # Tạo cấu trúc thư mục phân cấp
        dir_path = os.path.join(self.cache_dir, cache_key[:2], cache_key[2:4])
        os.makedirs(dir_path, exist_ok=True)
        return os.path.join(dir_path, f"{cache_key}.cache")

    def _maybe_cleanup(self):
        """Dọn dẹp cache nếu cần thiết."""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_expired()
            self.last_cleanup = current_time

    def get(
        self,
        key: str,
        default: Any = None,
        tool_name: Optional[str] = None,
        tool_args: Optional[Dict[str, Any]] = None
    ) -> Any:
        """
        Lấy giá trị từ cache với multi-tier lookup.

        Args:
            key: Cache key
            default: Giá trị mặc định nếu không tìm thấy
            tool_name: Tên tool (cho ReAct caching)
            tool_args: Arguments của tool

        Returns:
            Giá trị từ cache hoặc default
        """
        # Generate cache key
        cache_key = self._generate_cache_key(key, tool_name, tool_args)

        # Cleanup if needed
        self._maybe_cleanup()

        # 1. Try memory cache first
        with self.memory_lock:
            if cache_key in self.memory_cache:
                entry = self.memory_cache[cache_key]
                if not entry.is_expired():
                    entry.touch()
                    # Move to end (LRU)
                    self.memory_cache.move_to_end(cache_key)
                    if self.enable_statistics:
                        self.stats["memory_hits"] += 1
                    return entry.value
                else:
                    # Remove expired entry
                    del self.memory_cache[cache_key]

        # 2. Try disk cache
        if self.enable_disk_cache:
            disk_value = self._get_from_disk(cache_key)
            if disk_value is not None:
                # Add back to memory cache
                self._add_to_memory(cache_key, disk_value)
                if self.enable_statistics:
                    self.stats["disk_hits"] += 1
                return disk_value

        # 3. Try Redis cache
        if self.enable_redis_cache and self.redis_client:
            redis_value = self._get_from_redis(cache_key)
            if redis_value is not None:
                # Add back to memory and disk cache
                self._add_to_memory(cache_key, redis_value)
                if self.enable_disk_cache:
                    self._add_to_disk(cache_key, redis_value)
                if self.enable_statistics:
                    self.stats["redis_hits"] += 1
                return redis_value

        # 4. Try semantic search if enabled
        if self.enable_semantic_search and key:  # Only for text keys
            similar_value = self._find_similar_cached_value(key)
            if similar_value is not None:
                if self.enable_statistics:
                    self.stats["semantic_hits"] += 1
                return similar_value

        # Cache miss
        if self.enable_statistics:
            self.stats["misses"] += 1
        return default

    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        content_type: str = "default",
        metadata: Optional[Dict[str, Any]] = None,
        tool_name: Optional[str] = None,
        tool_args: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Lưu giá trị vào cache với multi-tier storage.

        Args:
            key: Cache key
            value: Giá trị cần lưu
            ttl: TTL tùy chỉnh (giây)
            content_type: Loại nội dung
            metadata: Metadata bổ sung
            tool_name: Tên tool (cho ReAct caching)
            tool_args: Arguments của tool
        """
        # Generate cache key
        cache_key = self._generate_cache_key(key, tool_name, tool_args)

        # Calculate TTL
        if ttl is None:
            ttl = self._calculate_adaptive_ttl(key, content_type)

        # Create cache entry
        entry = CacheEntry(
            value=value,
            ttl=ttl,
            metadata=metadata,
            content_type=content_type,
            tool_name=tool_name,
            tool_args=tool_args
        )

        # 1. Add to memory cache
        self._add_to_memory(cache_key, entry)

        # 2. Add to disk cache
        if self.enable_disk_cache:
            self._add_to_disk(cache_key, entry)

        # 3. Add to Redis cache
        if self.enable_redis_cache and self.redis_client:
            self._add_to_redis(cache_key, entry)

        if self.enable_statistics:
            self.stats["sets"] += 1

    def delete(self, key: str, tool_name: Optional[str] = None, tool_args: Optional[Dict[str, Any]] = None) -> bool:
        """
        Xóa giá trị khỏi tất cả cache tiers.

        Args:
            key: Cache key
            tool_name: Tên tool (cho ReAct caching)
            tool_args: Arguments của tool

        Returns:
            True nếu xóa thành công
        """
        cache_key = self._generate_cache_key(key, tool_name, tool_args)
        deleted = False

        # 1. Delete from memory cache
        with self.memory_lock:
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
                deleted = True

        # 2. Delete from disk cache
        if self.enable_disk_cache:
            if self._delete_from_disk(cache_key):
                deleted = True

        # 3. Delete from Redis cache
        if self.enable_redis_cache and self.redis_client:
            if self._delete_from_redis(cache_key):
                deleted = True

        if deleted and self.enable_statistics:
            self.stats["deletes"] += 1

        return deleted

    def clear(self) -> None:
        """Xóa tất cả cache."""
        # Clear memory cache
        with self.memory_lock:
            self.memory_cache.clear()

        # Clear disk cache
        if self.enable_disk_cache:
            self._clear_disk_cache()

        # Clear Redis cache
        if self.enable_redis_cache and self.redis_client:
            self._clear_redis_cache()

        logger.info("Cleared all cache tiers")

    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê cache.

        Returns:
            Dictionary chứa thống kê
        """
        stats = dict(self.stats)

        # Add memory cache stats
        with self.memory_lock:
            stats["memory_size"] = len(self.memory_cache)
            stats["memory_max_size"] = self.max_memory_size

        # Add disk cache stats
        if self.enable_disk_cache:
            stats["disk_size"] = len(self.disk_metadata.get("entries", {}))
            stats["disk_max_size"] = self.max_disk_size

        # Add Redis cache stats
        if self.enable_redis_cache and self.redis_client:
            try:
                redis_info = self.redis_client.info("memory")
                stats["redis_memory_used"] = redis_info.get("used_memory", 0)
            except Exception as e:
                logger.error(f"Failed to get Redis stats: {str(e)}")

        # Calculate hit ratios
        total_requests = stats["memory_hits"] + stats["disk_hits"] + stats["redis_hits"] + stats["semantic_hits"] + stats["misses"]
        if total_requests > 0:
            stats["hit_ratio"] = (stats["memory_hits"] + stats["disk_hits"] + stats["redis_hits"] + stats["semantic_hits"]) / total_requests
            stats["memory_hit_ratio"] = stats["memory_hits"] / total_requests
        else:
            stats["hit_ratio"] = 0.0
            stats["memory_hit_ratio"] = 0.0

        return stats

    # Helper methods for memory cache operations
    def _add_to_memory(self, cache_key: str, entry: CacheEntry):
        """Thêm entry vào memory cache."""
        with self.memory_lock:
            # Remove if already exists
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]

            # Add new entry
            self.memory_cache[cache_key] = entry

            # Evict if over limit
            if len(self.memory_cache) > self.max_memory_size:
                # Remove least recently used (first item)
                self.memory_cache.popitem(last=False)
                if self.enable_statistics:
                    self.stats["evictions"] += 1

    # Helper methods for disk cache operations
    def _get_from_disk(self, cache_key: str) -> Optional[Any]:
        """Lấy giá trị từ disk cache."""
        if cache_key not in self.disk_metadata.get("entries", {}):
            return None

        entry_info = self.disk_metadata["entries"][cache_key]
        file_path = self._get_disk_file_path(cache_key)

        if not os.path.exists(file_path):
            # Remove from metadata if file doesn't exist
            del self.disk_metadata["entries"][cache_key]
            self._save_disk_metadata()
            return None

        try:
            # Read file
            with open(file_path, "rb") as f:
                data = f.read()

            # Decompress if needed
            was_compressed = entry_info.get("compressed", False)
            data = self._decompress_data(data, was_compressed)

            # Deserialize
            entry = pickle.loads(data)

            # Check if expired
            if entry.is_expired():
                # Remove expired entry
                os.remove(file_path)
                del self.disk_metadata["entries"][cache_key]
                self._save_disk_metadata()
                return None

            return entry.value
        except Exception as e:
            logger.error(f"Failed to read from disk cache: {str(e)}")
            return None

    def _add_to_disk(self, cache_key: str, entry: CacheEntry):
        """Thêm entry vào disk cache."""
        try:
            # Serialize entry
            data = pickle.dumps(entry)

            # Compress if beneficial
            compressed_data, was_compressed = self._compress_data(data)

            # Write to file
            file_path = self._get_disk_file_path(cache_key)
            with open(file_path, "wb") as f:
                f.write(compressed_data)

            # Update metadata
            self.disk_metadata["entries"][cache_key] = {
                "file_path": file_path,
                "created_at": entry.created_at,
                "ttl": entry.ttl,
                "compressed": was_compressed,
                "size": len(compressed_data),
                "content_type": entry.content_type
            }

            # Evict if over limit
            if len(self.disk_metadata["entries"]) > self.max_disk_size:
                self._evict_disk_entries()

            self._save_disk_metadata()
        except Exception as e:
            logger.error(f"Failed to add to disk cache: {str(e)}")

    def _delete_from_disk(self, cache_key: str) -> bool:
        """Xóa entry khỏi disk cache."""
        if cache_key not in self.disk_metadata.get("entries", {}):
            return False

        try:
            file_path = self._get_disk_file_path(cache_key)
            if os.path.exists(file_path):
                os.remove(file_path)

            del self.disk_metadata["entries"][cache_key]
            self._save_disk_metadata()
            return True
        except Exception as e:
            logger.error(f"Failed to delete from disk cache: {str(e)}")
            return False

    def _clear_disk_cache(self):
        """Xóa tất cả disk cache."""
        try:
            # Remove all cache files
            for entry_info in self.disk_metadata.get("entries", {}).values():
                file_path = entry_info.get("file_path")
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)

            # Clear metadata
            self.disk_metadata["entries"] = {}
            self._save_disk_metadata()
        except Exception as e:
            logger.error(f"Failed to clear disk cache: {str(e)}")

    def _evict_disk_entries(self):
        """Loại bỏ các entries cũ nhất từ disk cache."""
        entries = self.disk_metadata.get("entries", {})
        if len(entries) <= self.max_disk_size:
            return

        # Sort by creation time
        sorted_entries = sorted(
            entries.items(),
            key=lambda x: x[1].get("created_at", 0)
        )

        # Remove oldest entries
        num_to_remove = len(entries) - self.max_disk_size + 100  # Remove extra to avoid frequent evictions
        for i, (cache_key, entry_info) in enumerate(sorted_entries):
            if i >= num_to_remove:
                break

            # Remove file
            file_path = entry_info.get("file_path")
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.error(f"Failed to remove disk cache file: {str(e)}")

            # Remove from metadata
            del entries[cache_key]

        self._save_disk_metadata()

    # Helper methods for Redis cache operations
    def _get_from_redis(self, cache_key: str) -> Optional[Any]:
        """Lấy giá trị từ Redis cache."""
        if not self.redis_client:
            return None

        try:
            data = self.redis_client.get(cache_key)
            if data is None:
                return None

            # Deserialize
            entry = pickle.loads(data)

            # Check if expired
            if entry.is_expired():
                self.redis_client.delete(cache_key)
                return None

            return entry.value
        except Exception as e:
            logger.error(f"Failed to get from Redis cache: {str(e)}")
            return None

    def _add_to_redis(self, cache_key: str, entry: CacheEntry):
        """Thêm entry vào Redis cache."""
        if not self.redis_client:
            return

        try:
            # Serialize entry
            data = pickle.dumps(entry)

            # Set with TTL
            self.redis_client.setex(cache_key, entry.ttl, data)
        except Exception as e:
            logger.error(f"Failed to add to Redis cache: {str(e)}")

    def _delete_from_redis(self, cache_key: str) -> bool:
        """Xóa entry khỏi Redis cache."""
        if not self.redis_client:
            return False

        try:
            result = self.redis_client.delete(cache_key)
            return result > 0
        except Exception as e:
            logger.error(f"Failed to delete from Redis cache: {str(e)}")
            return False

    def _clear_redis_cache(self):
        """Xóa tất cả Redis cache."""
        if not self.redis_client:
            return

        try:
            self.redis_client.flushdb()
        except Exception as e:
            logger.error(f"Failed to clear Redis cache: {str(e)}")

    # Semantic search methods
    def _find_similar_cached_value(self, query: str) -> Optional[Any]:
        """Tìm giá trị cache tương tự dựa trên semantic similarity."""
        if not self.enable_semantic_search or not self.embedding_model:
            return None

        try:
            # Get all cached queries from memory
            cached_queries = []
            with self.memory_lock:
                for cache_key, entry in self.memory_cache.items():
                    if hasattr(entry, 'tool_args') and entry.tool_args:
                        # Extract query from tool args if available
                        query_text = entry.tool_args.get('query', '')
                        if query_text:
                            cached_queries.append((cache_key, query_text, entry))

            if not cached_queries:
                return None

            # Calculate similarities
            best_match = None
            best_similarity = 0.0

            for cache_key, cached_query, entry in cached_queries:
                similarity = self._calculate_text_similarity(query, cached_query)
                if similarity > best_similarity and similarity >= self.similarity_threshold:
                    best_similarity = similarity
                    best_match = entry

            if best_match and not best_match.is_expired():
                return best_match.value

            return None
        except Exception as e:
            logger.error(f"Failed to find similar cached value: {str(e)}")
            return None

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Tính toán độ tương tự giữa hai văn bản."""
        if not text1 or not text2:
            return 0.0

        if text1 == text2:
            return 1.0

        try:
            if SENTENCE_TRANSFORMERS_AVAILABLE and hasattr(self.embedding_model, 'encode'):
                # Use sentence transformers
                embeddings = self.embedding_model.encode([text1, text2])
                # Calculate cosine similarity
                from sklearn.metrics.pairwise import cosine_similarity
                similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
                return float(similarity)

            elif SKLEARN_AVAILABLE and hasattr(self.embedding_model, 'fit_transform'):
                # Use TF-IDF
                tfidf_matrix = self.embedding_model.fit_transform([text1, text2])
                similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
                return float(similarity)

            else:
                # Fallback to simple word overlap
                words1 = set(text1.lower().split())
                words2 = set(text2.lower().split())
                if not words1 or not words2:
                    return 0.0

                intersection = len(words1.intersection(words2))
                union = len(words1.union(words2))
                return intersection / union if union > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate text similarity: {str(e)}")
            return 0.0

    # Cleanup methods
    def _cleanup_expired(self):
        """Dọn dẹp các entries hết hạn từ tất cả cache tiers."""
        # Cleanup memory cache
        with self.memory_lock:
            expired_keys = []
            for cache_key, entry in list(self.memory_cache.items()):
                if entry.is_expired():
                    expired_keys.append(cache_key)

            for cache_key in expired_keys:
                del self.memory_cache[cache_key]
                if self.enable_statistics:
                    self.stats["evictions"] += 1

        # Cleanup disk cache
        if self.enable_disk_cache:
            self._cleanup_expired_disk_entries()

        logger.info(f"Cleaned up {len(expired_keys)} expired entries from memory cache")

    def _cleanup_expired_disk_entries(self):
        """Dọn dẹp các entries hết hạn từ disk cache."""
        if not self.enable_disk_cache:
            return

        expired_keys = []
        current_time = time.time()

        for cache_key, entry_info in list(self.disk_metadata.get("entries", {}).items()):
            created_at = entry_info.get("created_at", 0)
            ttl = entry_info.get("ttl", self.default_ttl)

            if current_time - created_at > ttl:
                expired_keys.append(cache_key)

        # Remove expired entries
        for cache_key in expired_keys:
            self._delete_from_disk(cache_key)

        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired entries from disk cache")


# Convenience functions for backward compatibility
def create_cache_key(prefix: str, *args, **kwargs) -> str:
    """
    Tạo cache key từ prefix và parameters.

    Args:
        prefix: Tiền tố cho key
        *args: Các tham số vị trí
        **kwargs: Các tham số từ khóa

    Returns:
        Cache key
    """
    args_str = json.dumps(args, sort_keys=True)
    kwargs_str = json.dumps(kwargs, sort_keys=True)
    key_hash = hashlib.md5(f"{args_str}:{kwargs_str}".encode()).hexdigest()
    return f"{prefix}:{key_hash}"


def determine_ttl_by_content_type(content_type: str, base_ttl: int = 3600) -> int:
    """
    Xác định TTL dựa trên loại nội dung.

    Args:
        content_type: Loại nội dung
        base_ttl: TTL cơ bản

    Returns:
        TTL được điều chỉnh
    """
    # Sử dụng logic từ UnifiedCache
    cache = UnifiedCache()
    return cache._calculate_adaptive_ttl("", content_type)


# Global cache instance
_global_cache_instance = None

def get_cache(
    default_ttl: int = 3600,
    max_memory_size: int = 1000,
    cache_dir: Optional[str] = None,
    enable_disk_cache: bool = True,
    enable_redis_cache: bool = False,
    **kwargs
) -> UnifiedCache:
    """
    Lấy global cache instance.

    Args:
        default_ttl: TTL mặc định
        max_memory_size: Kích thước memory cache
        cache_dir: Thư mục cache
        enable_disk_cache: Bật disk cache
        enable_redis_cache: Bật Redis cache
        **kwargs: Các tham số khác

    Returns:
        UnifiedCache instance
    """
    global _global_cache_instance
    if _global_cache_instance is None:
        _global_cache_instance = UnifiedCache(
            default_ttl=default_ttl,
            max_memory_size=max_memory_size,
            cache_dir=cache_dir,
            enable_disk_cache=enable_disk_cache,
            enable_redis_cache=enable_redis_cache,
            **kwargs
        )
    return _global_cache_instance
