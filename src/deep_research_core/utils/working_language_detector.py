#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Working Language Detector - Immediate use version.

Đây là version hoạt động ngay lập tức của language detector đ<PERSON> sử dụng
trong khi UnifiedLanguageDetector đang được debug.
"""

import re
import logging
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)


class WorkingLanguageDetector:
    """
    Working language detector với tính năng cơ bản.
    
    Tính năng:
    - Character-based detection
    - Word-based detection
    - Vietnamese optimizations
    - Fallback mechanisms
    """
    
    def __init__(self, default_language: str = "en", min_confidence: float = 0.3):
        """
        Khởi tạo WorkingLanguageDetector.
        
        Args:
            default_language: Ngôn ngữ mặc định
            min_confidence: Confidence tối thiểu
        """
        self.default_language = default_language
        self.min_confidence = min_confidence
        
        # Vietnamese characters
        self.vietnamese_chars = set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ")
        
        # Common words for each language
        self.common_words = {
            "vi": {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"},
            "en": {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"},
            "fr": {"le", "la", "les", "un", "une", "des", "et", "est", "en", "à", "que", "qui", "dans", "pour", "pas"},
            "de": {"der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "auf", "für", "von", "nicht", "ein"},
            "es": {"el", "la", "los", "las", "un", "una", "y", "es", "en", "de", "que", "por", "para", "con", "no"},
            "it": {"il", "la", "i", "le", "un", "una", "e", "è", "in", "di", "che", "per", "non", "con", "sono"},
            "pt": {"o", "a", "os", "as", "um", "uma", "e", "é", "em", "de", "que", "para", "não", "com", "são"},
            "ru": {"и", "в", "не", "на", "я", "быть", "он", "с", "что", "а", "по", "это", "она", "этот", "к"}
        }
        
        # Language names
        self.language_names = {
            "en": "English", "vi": "Vietnamese", "fr": "French", "de": "German",
            "es": "Spanish", "it": "Italian", "pt": "Portuguese", "ru": "Russian"
        }
        
        logger.info("WorkingLanguageDetector initialized")
    
    def detect(self, text: str) -> str:
        """Simple detection method."""
        lang, _ = self.detect_language(text, return_confidence=True)
        return lang
    
    def detect_language(self, text: str, return_confidence: bool = True) -> Tuple[str, float]:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ
            return_confidence: Trả về confidence score

        Returns:
            Tuple[str, float]: (language_code, confidence)
        """
        if not text or len(text.strip()) < 3:
            if return_confidence:
                return self.default_language, 0.5
            return self.default_language

        text = text.strip().lower()

        # Method 1: Character-based detection
        char_scores = self._detect_by_chars(text)

        # Method 2: Word-based detection
        word_scores = self._detect_by_words(text)

        # Debug output
        logger.debug(f"Text: '{text}'")
        logger.debug(f"Char scores: {char_scores}")
        logger.debug(f"Word scores: {word_scores}")

        # Special handling for Vietnamese - check character detection first
        vi_char_score = char_scores.get("vi", 0.0)
        if vi_char_score > 0.1:  # Strong Vietnamese character presence
            logger.debug(f"Strong Vietnamese character presence: {vi_char_score:.3f}")
            if return_confidence:
                return "vi", min(0.9, 0.5 + vi_char_score)
            return "vi"

        # Combine scores for other cases
        combined_scores = {}
        all_langs = set(list(char_scores.keys()) + list(word_scores.keys()))

        for lang in all_langs:
            char_score = char_scores.get(lang, 0.0)
            word_score = word_scores.get(lang, 0.0)

            # Special handling for Vietnamese - prioritize character detection
            if lang == "vi":
                # Even with lower character presence, still give high weight
                combined_scores[lang] = char_score * 0.8 + word_score * 0.2
            else:
                # For other languages, balance char and word detection
                combined_scores[lang] = char_score * 0.4 + word_score * 0.6

        logger.debug(f"Combined scores: {combined_scores}")

        # Find best language
        if combined_scores:
            best_lang = max(combined_scores.keys(), key=lambda k: combined_scores[k])
            best_score = combined_scores[best_lang]

            logger.debug(f"Best: {best_lang} ({best_score:.3f})")

            # Special handling for Vietnamese - lower threshold
            if best_lang == "vi" and best_score > 0.05:
                if return_confidence:
                    return best_lang, best_score
                return best_lang
            elif best_score >= self.min_confidence:
                if return_confidence:
                    return best_lang, best_score
                return best_lang

        # Fallback to default
        if return_confidence:
            return self.default_language, 0.5
        return self.default_language
    
    def _detect_by_chars(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ dựa trên ký tự đặc trưng."""
        scores = {}
        
        # Count Vietnamese characters
        vi_char_count = sum(1 for char in text if char in self.vietnamese_chars)
        total_chars = len([c for c in text if c.isalpha()])
        
        if total_chars > 0:
            scores["vi"] = vi_char_count / total_chars
            
            # English detection (basic Latin characters)
            english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
            scores["en"] = english_chars / total_chars if total_chars > 0 else 0.0
            
            # Adjust English score if Vietnamese characters are present
            if scores["vi"] > 0:
                scores["en"] = max(0.0, scores["en"] - scores["vi"])
        
        return scores
    
    def _detect_by_words(self, text: str) -> Dict[str, float]:
        """Phát hiện ngôn ngữ dựa trên từ phổ biến."""
        scores = {}
        
        # Extract words
        words = re.findall(r'\b\w+\b', text.lower())
        if not words:
            return scores
        
        for lang, common_words in self.common_words.items():
            count = sum(1 for word in words if word in common_words)
            scores[lang] = count / len(words) if len(words) > 0 else 0.0
        
        return scores
    
    def is_vietnamese(self, text: str, min_confidence: float = None) -> bool:
        """Kiểm tra xem text có phải tiếng Việt không."""
        if min_confidence is None:
            min_confidence = self.min_confidence
        
        lang, conf = self.detect_language(text, return_confidence=True)
        return lang == "vi" and conf >= min_confidence
    
    def is_english(self, text: str, min_confidence: float = None) -> bool:
        """Kiểm tra xem text có phải tiếng Anh không."""
        if min_confidence is None:
            min_confidence = self.min_confidence
        
        lang, conf = self.detect_language(text, return_confidence=True)
        return lang == "en" and conf >= min_confidence
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Lấy danh sách ngôn ngữ được hỗ trợ."""
        return self.language_names.copy()
    
    def get_stats(self) -> Dict[str, any]:
        """Lấy thống kê về detector."""
        return {
            "supported_languages": len(self.language_names),
            "default_language": self.default_language,
            "min_confidence": self.min_confidence,
            "type": "WorkingLanguageDetector"
        }


# Convenience functions
def detect_language(text: str) -> Tuple[str, float]:
    """
    Convenience function để phát hiện ngôn ngữ.
    
    Args:
        text: Text cần phát hiện ngôn ngữ
        
    Returns:
        Tuple[str, float]: (language_code, confidence)
    """
    detector = WorkingLanguageDetector()
    return detector.detect_language(text, return_confidence=True)


def is_vietnamese(text: str) -> bool:
    """Convenience function kiểm tra tiếng Việt."""
    detector = WorkingLanguageDetector()
    return detector.is_vietnamese(text)


def is_english(text: str) -> bool:
    """Convenience function kiểm tra tiếng Anh."""
    detector = WorkingLanguageDetector()
    return detector.is_english(text)


# Global detector instance
_global_working_detector = None

def get_working_language_detector() -> WorkingLanguageDetector:
    """
    Lấy global working detector instance.
    
    Returns:
        WorkingLanguageDetector instance
    """
    global _global_working_detector
    if _global_working_detector is None:
        _global_working_detector = WorkingLanguageDetector()
    return _global_working_detector
