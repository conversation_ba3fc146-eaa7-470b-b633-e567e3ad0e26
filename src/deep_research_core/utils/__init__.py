#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> tiện ích cho deep_research_core.
"""

from typing import Dict, Any, List, Optional

# Các module tiện ích có thể được import từ đây

import warnings

# Hi<PERSON>n thị cảnh báo về module utils
warnings.warn(
    "Module 'utils' đang được tái cấu trúc. <PERSON>ác chức năng đã được di chuyển "
    "sang các module mới như 'credibility', 'integrations'. <PERSON>ui lòng cập nhật "
    "mã nguồn của bạn để sử dụng các module mới.",
    DeprecationWarning,
    stacklevel=2
)

# Import các adapter
try:
    from .adapter import (
        get_credibility_adapter,
        CredibilityAdapter,
        LegacyImportAdapter
    )
except ImportError:
    # Fallback for different import contexts
    try:
        from deep_research_core.utils.adapter import (
            get_credibility_adapter,
            CredibilityAdapter,
            LegacyImportAdapter
        )
    except ImportError:
        warnings.warn(
            "Không thể import adapter. Một số chức năng có thể không khả dụng.",
            ImportWarning,
            stacklevel=2
        )
        # Create dummy functions
        def get_credibility_adapter():
            return None
        class CredibilityAdapter:
            pass
        class LegacyImportAdapter:
            pass

# Tạo các proxy function
def detect_fake_news(*args, **kwargs):
    """
    Proxy function cho detect_fake_news.
    Đã được chuyển sang credibility.detectors.fake_news_detector.detect
    """
    return get_credibility_adapter().detect_fake_news(*args, **kwargs)

def evaluate_credibility(*args, **kwargs):
    """
    Proxy function cho evaluate_credibility.
    Đã được chuyển sang credibility.evaluators.credibility_evaluator.evaluate_url
    """
    return get_credibility_adapter().evaluate_credibility(*args, **kwargs)

def analyze_content_with_llm(*args, **kwargs):
    """
    Proxy function cho analyze_content_with_llm.
    Đã được chuyển sang integrations.llm.analyze_content
    """
    return get_credibility_adapter().analyze_content_with_llm(*args, **kwargs)

# Import các module tiện ích mới
try:
    from .unified_cache import (
        UnifiedCache, CacheEntry, create_cache_key, determine_ttl_by_content_type, get_cache
    )
    # Alias for backward compatibility
    Cache = UnifiedCache
except ImportError:
    try:
        from .cache_utils import (
            Cache, CacheEntry, create_cache_key, determine_ttl_by_content_type, get_cache
        )
    except ImportError:
        warnings.warn(
            "Không thể import cache_utils. Chức năng cache có thể không khả dụng.",
            ImportWarning,
            stacklevel=2
        )

try:
    from .query_utils import (
        clean_query, extract_keywords, decompose_query, optimize_query,
        calculate_query_complexity, extract_entities, extract_main_topic,
        extract_facets, generate_alternative_queries, query_similarity
    )
except ImportError:
    warnings.warn(
        "Không thể import query_utils. Chức năng xử lý truy vấn có thể không khả dụng.",
        ImportWarning,
        stacklevel=2
    )