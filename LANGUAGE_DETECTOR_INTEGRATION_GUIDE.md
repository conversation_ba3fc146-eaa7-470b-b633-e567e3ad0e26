# Language Detector Integration Guide

## 🚀 Production Deployment Guide

### 📋 Tổng quan

Language Detector System đã được tối ưu hóa và sẵn sàng cho production deployment với:
- **OptimizedLanguageDetector**: High-performance, thread-safe detector
- **LanguageDetectorManager**: Unified management system
- **Production configurations**: Optimized cho different environments
- **Comprehensive monitoring**: Performance tracking và statistics

### 🏗️ Architecture Overview

```
Application Layer
    ↓
Language Detector API
    ↓
LanguageDetectorManager (Coordinator)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│ OptimizedLD     │ WorkingLD       │ AdvancedLD      │
│ (Primary)       │ (Fallback)      │ (Accuracy)      │
└─────────────────┴─────────────────┴─────────────────┘
    ↓
Caching Layer + Statistics
```

## 🔧 Installation & Setup

### 1. Basic Installation

```python
# Import the optimized detector
from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
from deep_research_core.utils.language_detector_config import get_predefined_config

# Create detector with production config
config = get_predefined_config('prod_balanced')
detector = OptimizedLanguageDetector(config)
```

### 2. Configuration-based Setup

```python
from deep_research_core.utils.language_detector_config import (
    get_config, Environment, PerformanceProfile
)

# Production setup với high performance
config = get_config(
    environment=Environment.PRODUCTION,
    performance_profile=PerformanceProfile.HIGH_SPEED
)

detector = OptimizedLanguageDetector(config)
```

### 3. Use Case-specific Setup

```python
from deep_research_core.utils.language_detector_config import get_use_case_config

# Web application với high traffic
config = get_use_case_config('web_app_high_traffic')
detector = OptimizedLanguageDetector(config)

# Vietnamese-focused application
config = get_use_case_config('vietnamese_app')
detector = OptimizedLanguageDetector(config)
```

## 📊 Usage Examples

### Basic Usage

```python
from deep_research_core.utils.optimized_language_detector import (
    detect_language_optimized, is_vietnamese_optimized
)

# Simple detection
language = detect_language_optimized("Xin chào thế giới")  # Returns: "vi"
language = detect_language_optimized("Hello world")       # Returns: "en"

# Vietnamese check
is_vietnamese = is_vietnamese_optimized("Xin chào")  # Returns: True
```

### Advanced Usage

```python
from deep_research_core.utils.optimized_language_detector import (
    OptimizedLanguageDetector, DetectionMethod
)

# Create detector
detector = OptimizedLanguageDetector()

# Detection với confidence
lang, confidence = detector.detect_with_confidence("Xin chào thế giới")
print(f"Language: {lang}, Confidence: {confidence:.2f}")

# Different detection methods
lang = detector.detect("Hello world", DetectionMethod.CHARACTER_BASED)
lang = detector.detect("Hello world", DetectionMethod.WORD_BASED)
lang = detector.detect("Hello world", DetectionMethod.HYBRID)  # Recommended
```

### Batch Processing

```python
def process_documents(documents):
    """Process multiple documents efficiently."""
    detector = OptimizedLanguageDetector(get_use_case_config('batch_processing'))
    
    results = []
    for doc in documents:
        lang, conf = detector.detect_with_confidence(doc['content'])
        results.append({
            'id': doc['id'],
            'language': lang,
            'confidence': conf
        })
    
    return results
```

## 🔍 Monitoring & Statistics

### Performance Monitoring

```python
# Get comprehensive statistics
stats = detector.get_stats()

print(f"Total detections: {stats['total_detections']}")
print(f"Success rate: {stats['success_rate']:.1%}")
print(f"Average time: {stats['avg_time_ms']:.2f} ms")
print(f"Cache hit rate: {stats['cache_hit_rate']:.1%}")
```

### Production Monitoring Setup

```python
import logging
from deep_research_core.utils.language_detector_config import get_logging_config

# Setup logging
logging_config = get_logging_config('production')
logging.basicConfig(
    level=getattr(logging, logging_config['level']),
    format=logging_config['format']
)

# Monitor performance
def monitor_detector_performance(detector):
    stats = detector.get_stats()
    
    # Log performance metrics
    if stats['avg_time_ms'] > 10:  # Alert if detection takes > 10ms
        logging.warning(f"Slow detection: {stats['avg_time_ms']:.2f}ms")
    
    if stats['cache_hit_rate'] < 0.5:  # Alert if cache hit rate < 50%
        logging.warning(f"Low cache hit rate: {stats['cache_hit_rate']:.1%}")
    
    if stats['success_rate'] < 0.95:  # Alert if success rate < 95%
        logging.error(f"Low success rate: {stats['success_rate']:.1%}")
```

## 🌐 Web Application Integration

### Flask Integration

```python
from flask import Flask, request, jsonify
from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
from deep_research_core.utils.language_detector_config import get_use_case_config

app = Flask(__name__)

# Initialize detector
detector = OptimizedLanguageDetector(get_use_case_config('web_app_high_traffic'))

@app.route('/detect-language', methods=['POST'])
def detect_language():
    try:
        data = request.get_json()
        text = data.get('text', '')
        
        if not text:
            return jsonify({'error': 'Text is required'}), 400
        
        language, confidence = detector.detect_with_confidence(text)
        
        return jsonify({
            'language': language,
            'confidence': confidence,
            'language_name': detector.language_names.get(language, language)
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/detector-stats', methods=['GET'])
def get_stats():
    return jsonify(detector.get_stats())

if __name__ == '__main__':
    app.run(debug=False)
```

### FastAPI Integration

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
from deep_research_core.utils.language_detector_config import get_use_case_config

app = FastAPI()

# Initialize detector
detector = OptimizedLanguageDetector(get_use_case_config('web_app_high_traffic'))

class TextRequest(BaseModel):
    text: str

class LanguageResponse(BaseModel):
    language: str
    confidence: float
    language_name: str

@app.post("/detect-language", response_model=LanguageResponse)
async def detect_language(request: TextRequest):
    try:
        language, confidence = detector.detect_with_confidence(request.text)
        
        return LanguageResponse(
            language=language,
            confidence=confidence,
            language_name=detector.language_names.get(language, language)
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/detector-stats")
async def get_stats():
    return detector.get_stats()
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Set environment variables
ENV PYTHONPATH=/app/src
ENV DETECTOR_CONFIG=prod_balanced

# Expose port
EXPOSE 8000

# Run application
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  language-detector:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DETECTOR_CONFIG=prod_high_traffic
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - language-detector
    restart: unless-stopped
```

## 📈 Performance Optimization

### High-Traffic Optimization

```python
# Configuration cho high-traffic applications
high_traffic_config = {
    'cache_size': 10000,
    'min_confidence': 0.2,  # Lower threshold for faster decisions
    'enable_caching': True,
    'enable_logging': False,  # Disable detailed logging for performance
}

detector = OptimizedLanguageDetector(high_traffic_config)
```

### Memory Optimization

```python
# Configuration cho memory-constrained environments
low_memory_config = {
    'cache_size': 500,
    'enable_caching': True,
    'min_confidence': 0.3,
}

detector = OptimizedLanguageDetector(low_memory_config)

# Periodic cache cleanup
import threading
import time

def cache_cleanup():
    while True:
        time.sleep(3600)  # Every hour
        detector.clear_cache()

cleanup_thread = threading.Thread(target=cache_cleanup, daemon=True)
cleanup_thread.start()
```

## 🔒 Security Considerations

### Input Validation

```python
def safe_detect_language(text):
    """Safe language detection với input validation."""
    
    # Input validation
    if not isinstance(text, str):
        raise ValueError("Text must be a string")
    
    if len(text) > 10000:  # Limit text length
        text = text[:10000]
    
    # Sanitize input (remove potential harmful content)
    import re
    text = re.sub(r'[^\w\s\u00C0-\u017F\u1EA0-\u1EF9]', '', text)
    
    return detector.detect_with_confidence(text)
```

### Rate Limiting

```python
from functools import wraps
import time
from collections import defaultdict

# Simple rate limiter
request_counts = defaultdict(list)

def rate_limit(max_requests=100, window=60):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            client_ip = request.remote_addr  # Flask example
            now = time.time()
            
            # Clean old requests
            request_counts[client_ip] = [
                req_time for req_time in request_counts[client_ip]
                if now - req_time < window
            ]
            
            # Check rate limit
            if len(request_counts[client_ip]) >= max_requests:
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            
            request_counts[client_ip].append(now)
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

@app.route('/detect-language', methods=['POST'])
@rate_limit(max_requests=1000, window=60)  # 1000 requests per minute
def detect_language():
    # ... implementation
    pass
```

## 🎯 Best Practices

### 1. Configuration Management
- Sử dụng environment-specific configurations
- Validate configurations trước khi deployment
- Monitor configuration changes

### 2. Error Handling
- Implement comprehensive error handling
- Log errors appropriately
- Provide meaningful error messages

### 3. Performance Monitoring
- Monitor detection latency
- Track cache hit rates
- Alert on performance degradation

### 4. Scaling
- Use horizontal scaling cho high traffic
- Implement load balancing
- Consider caching strategies

### 5. Maintenance
- Regular performance reviews
- Update configurations based on usage patterns
- Monitor for new language requirements

---

**Status**: ✅ Production Ready
**Last Updated**: Current session
**Recommendation**: Deploy với monitoring và gradual rollout
