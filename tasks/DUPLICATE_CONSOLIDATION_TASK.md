# TASK: CONSOLIDATION CÁC FILE TRÙNG LẶP

## TRẠNG THÁI: 🔴 CHƯA BẮT ĐẦU

## MỤC TIÊU
Loại bỏ các file và function trùng lặp giữa `src/deep_research_core` và `advanced_reasoning_engine/src/deep_research_core` để tối ưu hóa codebase.

## DANH SÁCH TASK CHI TIẾT

### PHASE 1: CRITICAL DUPLICATES (Ưu tiên cực cao)

#### Task 1.1: Consolidate Cache Utilities ✅ COMPLETED
**Mô tả**: Merge 4 implementations cache khác nhau thành 1 unified system
**Files cần xử lý**:
- `src/deep_research_core/utils/cache_utils.py`
- `advanced_reasoning_engine/src/deep_research_core/utils/advanced_react_cache.py`
- `src/deep_research_core/advanced_reasoning/deepresearch_organized/src/deep_research_core/utils/cache_manager.py`
- `src/deep_research_core/advanced_reasoning/deepresearch_organized/src/deep_research_core/utils/query_analyzer_cache.py`

**Hành động**:
1. [x] Phân tích tính năng của từng implementation
2. [x] Tạo unified cache class với tất cả tính năng tốt nhất
3. [x] Test cache performance và functionality
4. [ ] Update imports trong toàn bộ codebase
5. [ ] Remove duplicate files

**Tiêu chí hoàn thành**: 
- ✅ Chỉ còn 1 cache implementation
- ✅ Tất cả tính năng được preserve
- ✅ Không có import errors
- ✅ Performance tests pass

---

#### Task 1.2: Merge Language Detection ⏳ TODO
**Mô tả**: Merge 3 implementations language detector giống hệt nhau
**Files cần xử lý**:
- `src/deep_research_core/advanced_reasoning/deepresearch_organized/src/deep_research_core/utils/language_detector.py`
- `advanced_reasoning_engine/src/deep_research_core/utils/language_detector.py`
- Related language detection functions scattered across utils

**Hành động**:
1. [ ] So sánh chi tiết 2 implementations chính
2. [ ] Chọn implementation tốt nhất (advanced_reasoning version)
3. [ ] Move to central location: `src/deep_research_core/utils/language_detector.py`
4. [ ] Update all imports
5. [ ] Remove duplicate files

**Tiêu chí hoàn thành**:
- ✅ Chỉ còn 1 language detector
- ✅ Tất cả language detection features hoạt động
- ✅ Imports updated successfully

---

#### Task 1.3: Unify Vietnamese Utils ⏳ TODO
**Mô tả**: Merge 3 implementations Vietnamese utilities với 90% code trùng lặp
**Files cần xử lý**:
- `src/deep_research_core/utils/vietnamese_utils.py`
- `advanced_reasoning_engine/src/deep_research_core/utils/vietnamese_utils.py`
- `src/deep_research_core/advanced_reasoning/deepresearch_organized/src/deep_research_core/utils/vietnamese_utils.py`

**Hành động**:
1. [ ] Phân tích differences giữa 3 implementations
2. [ ] Merge tất cả unique features vào 1 file
3. [ ] Standardize function signatures
4. [ ] Test với Vietnamese content samples
5. [ ] Update imports và remove duplicates

**Tiêu chí hoàn thành**:
- ✅ Chỉ còn 1 vietnamese_utils.py
- ✅ Tất cả Vietnamese processing functions hoạt động
- ✅ Tests pass với Vietnamese content

---

#### Task 1.4: Merge Memory Integration ⏳ TODO
**Mô tả**: Merge 2 implementations memory integration hoàn toàn giống nhau
**Files cần xử lý**:
- `src/deep_research_core/advanced_reasoning/deepresearch_organized/src/deep_research_core/utils/memory_integration.py`
- `advanced_reasoning_engine/src/deep_research_core/utils/memory_integration.py`

**Hành động**:
1. [ ] Verify 2 files hoàn toàn giống nhau
2. [ ] Keep 1 file, remove duplicate
3. [ ] Update imports
4. [ ] Test memory integration functionality

**Tiêu chí hoàn thành**:
- ✅ Chỉ còn 1 memory_integration.py
- ✅ Memory functions hoạt động bình thường

---

### PHASE 2: HIGH PRIORITY

#### Task 2.1: Consolidate Multilingual Utils ⏳ TODO
**Files**: 2 multilingual_utils.py implementations
**Hành động**: Merge logic tương tự, preserve unique features

#### Task 2.2: Merge Web Search Agents ⏳ TODO  
**Files**: Multiple web_search_agent variations
**Hành động**: Consolidate thành 1 unified agent

#### Task 2.3: Unify Content Extractors ⏳ TODO
**Files**: 2 content_extractor implementations
**Hành động**: Merge extraction algorithms

---

### PHASE 3: MEDIUM PRIORITY

#### Task 3.1: Standardize Query Processing ⏳ TODO
**Files**: query_utils.py và query_analyzer_cache.py
**Hành động**: Centralize query processing logic

#### Task 3.2: Centralize Search Utilities ⏳ TODO
**Files**: Scattered search utility functions
**Hành động**: Tập trung vào 1 search_utils module

#### Task 3.3: Unify Error Handling ⏳ TODO
**Files**: Multiple error handling approaches
**Hành động**: Standardize error recovery system

---

### PHASE 4: CLEANUP

#### Task 4.1: Remove Unused Files ⏳ TODO
**Hành động**: Delete tất cả duplicate files đã được merged

#### Task 4.2: Update All Imports ⏳ TODO
**Hành động**: Fix tất cả import statements trong codebase

#### Task 4.3: Run Comprehensive Tests ⏳ TODO
**Hành động**: Verify không có regression sau khi merge

#### Task 4.4: Update Documentation ⏳ TODO
**Hành động**: Update docs để reflect new structure

---

## TRACKING PROGRESS

### Completed Tasks: 0/16
### Current Phase: Phase 1 - Critical Duplicates
### Next Task: Task 1.1 - Consolidate Cache Utilities

## NOTES
- Ưu tiên preserve tất cả functionality khi merge
- Test thoroughly sau mỗi merge
- Update imports ngay sau khi merge để tránh broken dependencies
- Backup files trước khi delete

## ESTIMATED TIMELINE
- Phase 1: 3-4 ngày
- Phase 2: 2-3 ngày  
- Phase 3: 2-3 ngày
- Phase 4: 1-2 ngày
- **Total**: 8-12 ngày
