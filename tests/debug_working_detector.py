#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script cho WorkingLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def debug_char_detection():
    """Debug character detection."""
    print("🔍 Debugging character detection...")
    
    # Vietnamese characters
    vietnamese_chars = set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ")
    
    text = "xin chào thế giới"
    print(f"Text: '{text}'")
    
    # Count Vietnamese characters
    vi_char_count = sum(1 for char in text if char in vietnamese_chars)
    total_chars = len([c for c in text if c.isalpha()])
    
    print(f"Vietnamese chars found: {vi_char_count}")
    print(f"Total alphabetic chars: {total_chars}")
    
    if total_chars > 0:
        vi_score = vi_char_count / total_chars
        print(f"Vietnamese score: {vi_score:.3f}")
        
        # English detection
        english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
        en_score = english_chars / total_chars
        print(f"English score: {en_score:.3f}")
        
        # Adjusted English score
        adjusted_en_score = max(0.0, en_score - vi_score)
        print(f"Adjusted English score: {adjusted_en_score:.3f}")
        
        if vi_score > 0.1:
            print("✅ Should detect Vietnamese")
        else:
            print("❌ Vietnamese score too low")
    
    return True

def debug_word_detection():
    """Debug word detection."""
    print("\n🔍 Debugging word detection...")
    
    vietnamese_words = {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"}
    english_words = {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"}
    
    text = "xin chào thế giới"
    print(f"Text: '{text}'")
    
    import re
    words = re.findall(r'\b\w+\b', text.lower())
    print(f"Words found: {words}")
    
    vi_word_count = sum(1 for word in words if word in vietnamese_words)
    en_word_count = sum(1 for word in words if word in english_words)
    
    print(f"Vietnamese words: {vi_word_count}")
    print(f"English words: {en_word_count}")
    
    if len(words) > 0:
        vi_word_score = vi_word_count / len(words)
        en_word_score = en_word_count / len(words)
        print(f"Vietnamese word score: {vi_word_score:.3f}")
        print(f"English word score: {en_word_score:.3f}")
    
    return True

def debug_combined_detection():
    """Debug combined detection."""
    print("\n🔍 Debugging combined detection...")
    
    # Simulate the detection logic
    text = "xin chào thế giới"
    
    # Character scores
    vietnamese_chars = set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ")
    vi_char_count = sum(1 for char in text if char in vietnamese_chars)
    total_chars = len([c for c in text if c.isalpha()])
    
    char_scores = {}
    if total_chars > 0:
        char_scores["vi"] = vi_char_count / total_chars
        english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
        char_scores["en"] = english_chars / total_chars
        if char_scores["vi"] > 0:
            char_scores["en"] = max(0.0, char_scores["en"] - char_scores["vi"])
    
    # Word scores
    import re
    words = re.findall(r'\b\w+\b', text.lower())
    word_scores = {}
    
    common_words = {
        "vi": {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"},
        "en": {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"}
    }
    
    for lang, common_words_set in common_words.items():
        count = sum(1 for word in words if word in common_words_set)
        word_scores[lang] = count / len(words) if len(words) > 0 else 0.0
    
    print(f"Character scores: {char_scores}")
    print(f"Word scores: {word_scores}")
    
    # Combine scores
    combined_scores = {}
    all_langs = set(list(char_scores.keys()) + list(word_scores.keys()))
    
    for lang in all_langs:
        char_score = char_scores.get(lang, 0.0)
        word_score = word_scores.get(lang, 0.0)
        
        if lang == "vi":
            combined_scores[lang] = char_score * 0.7 + word_score * 0.3
        else:
            combined_scores[lang] = char_score * 0.4 + word_score * 0.6
    
    print(f"Combined scores: {combined_scores}")
    
    if combined_scores:
        best_lang = max(combined_scores.keys(), key=lambda k: combined_scores[k])
        best_score = combined_scores[best_lang]
        print(f"Best: {best_lang} ({best_score:.3f})")
        
        if best_lang == "vi" and best_score > 0.05:
            print("✅ Should detect Vietnamese")
        else:
            print("❌ Vietnamese not detected")
    
    return True

def main():
    """Main debug function."""
    print("🚀 Starting WorkingLanguageDetector debug...\n")
    
    debug_char_detection()
    debug_word_detection()
    debug_combined_detection()
    
    print("\n🎉 Debug completed!")

if __name__ == "__main__":
    main()
