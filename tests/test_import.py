#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test import cho UnifiedLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_import():
    """Test import step by step."""
    print("🧪 Testing imports...")
    
    try:
        print("1. Testing basic imports...")
        import re
        import os
        import string
        import hashlib
        import logging
        import pickle
        from typing import Dict, Any, List, Optional, Union, Set, Tuple
        from collections import Counter, defaultdict
        print("✅ Basic imports OK")
        
        print("2. Testing module import...")
        from deep_research_core.utils import unified_language_detector
        print("✅ Module import OK")
        
        print("3. Testing class import...")
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        print("✅ Class import OK")
        
        print("4. Testing class instantiation...")
        detector = UnifiedLanguageDetector(use_external_libraries=False, enable_cache=False)
        print("✅ Class instantiation OK")
        
        print("5. Testing simple method...")
        result = detector.detect("Hello")
        print(f"✅ Simple method OK: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error at step: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import()
    sys.exit(0 if success else 1)
