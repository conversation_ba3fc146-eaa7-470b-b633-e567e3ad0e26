#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script tổng hợp cho tất cả language detectors.
"""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_working_detector():
    """Test WorkingLanguageDetector."""
    print("🧪 Testing WorkingLanguageDetector...")
    
    try:
        from deep_research_core.utils.working_language_detector import WorkingLanguageDetector
        
        detector = WorkingLanguageDetector()
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("Bonjour le monde", "fr"),
            ("Hola mundo", "es"),
        ]
        
        correct = 0
        start_time = time.time()
        
        for text, expected in test_cases:
            result = detector.detect(text)
            if result == expected:
                correct += 1
        
        end_time = time.time()
        
        accuracy = correct / len(test_cases) * 100
        speed = len(test_cases) / (end_time - start_time)
        
        print(f"   ✅ Accuracy: {accuracy:.1f}% ({correct}/{len(test_cases)})")
        print(f"   ⚡ Speed: {speed:.1f} detections/second")
        
        return {
            "name": "WorkingLanguageDetector",
            "accuracy": accuracy,
            "speed": speed,
            "working": True,
            "features": ["char_detection", "word_detection", "vietnamese_optimized"]
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "WorkingLanguageDetector", "working": False, "error": str(e)}

def test_multilingual_utils():
    """Test multilingual_utils functions."""
    print("\n🧪 Testing multilingual_utils...")
    
    try:
        from deep_research_core.utils.multilingual_utils import (
            detect_language, detect_vietnamese, normalize_text, 
            adapt_prompt_for_vietnamese, translate_keywords
        )
        
        # Test detect_language
        lang = detect_language("Xin chào thế giới")
        print(f"   detect_language: 'Xin chào thế giới' -> {lang}")
        
        # Test detect_vietnamese
        is_vi = detect_vietnamese("Xin chào thế giới")
        print(f"   detect_vietnamese: {is_vi}")
        
        # Test normalize_text
        normalized = normalize_text("  Xin   chào   thế   giới  ")
        print(f"   normalize_text: normalized text length: {len(normalized)}")
        
        # Test adapt_prompt_for_vietnamese
        adapted = adapt_prompt_for_vietnamese("Tìm kiếm thông tin về AI")
        print(f"   adapt_prompt_for_vietnamese: adapted prompt length: {len(adapted)}")
        
        # Test translate_keywords
        translated = translate_keywords(["search", "web"], "en", "vi")
        print(f"   translate_keywords: {translated}")
        
        print(f"   ✅ All multilingual_utils functions work")
        
        return {
            "name": "multilingual_utils",
            "working": True,
            "features": ["language_detection", "vietnamese_detection", "text_normalization", "prompt_adaptation", "keyword_translation"]
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "multilingual_utils", "working": False, "error": str(e)}

def test_vietnamese_utils():
    """Test vietnamese_utils functions."""
    print("\n🧪 Testing vietnamese_utils...")
    
    try:
        from deep_research_core.utils.vietnamese_utils import (
            is_vietnamese_text, remove_vietnamese_tones, 
            improve_vietnamese_paragraphs, identify_important_vietnamese_phrases
        )
        
        # Test is_vietnamese_text
        is_vi = is_vietnamese_text("Xin chào thế giới, đây là văn bản tiếng Việt")
        print(f"   is_vietnamese_text: {is_vi}")
        
        # Test remove_vietnamese_tones
        no_tones = remove_vietnamese_tones("Xin chào thế giới")
        print(f"   remove_vietnamese_tones: '{no_tones}'")
        
        # Test improve_vietnamese_paragraphs
        improved = improve_vietnamese_paragraphs("Xin   chào  .Thế   giới !")
        print(f"   improve_vietnamese_paragraphs: '{improved}'")
        
        # Test identify_important_vietnamese_phrases
        phrases = identify_important_vietnamese_phrases("Tìm kiếm thông tin về trí tuệ nhân tạo")
        print(f"   identify_important_vietnamese_phrases: {phrases}")
        
        print(f"   ✅ All vietnamese_utils functions work")
        
        return {
            "name": "vietnamese_utils",
            "working": True,
            "features": ["vietnamese_detection", "tone_removal", "text_improvement", "phrase_extraction"]
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "vietnamese_utils", "working": False, "error": str(e)}

def test_simple_detector():
    """Test SimpleLanguageDetector."""
    print("\n🧪 Testing SimpleLanguageDetector...")
    
    try:
        from deep_research_core.utils.simple_language_detector import SimpleLanguageDetector
        
        detector = SimpleLanguageDetector()
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
        ]
        
        correct = 0
        for text, expected in test_cases:
            result = detector.detect(text)
            if result == expected:
                correct += 1
        
        accuracy = correct / len(test_cases) * 100
        print(f"   ✅ Accuracy: {accuracy:.1f}% ({correct}/{len(test_cases)})")
        
        return {
            "name": "SimpleLanguageDetector",
            "accuracy": accuracy,
            "working": True,
            "features": ["basic_detection"]
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "SimpleLanguageDetector", "working": False, "error": str(e)}

def test_unified_detector():
    """Test UnifiedLanguageDetector."""
    print("\n🧪 Testing UnifiedLanguageDetector...")
    
    try:
        # Try with minimal config to avoid hanging
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        
        # Use timeout to prevent hanging
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("UnifiedLanguageDetector creation timed out")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10 second timeout
        
        try:
            detector = UnifiedLanguageDetector(
                use_external_libraries=False,
                enable_cache=False,
                resources_dir=None
            )
            signal.alarm(0)  # Cancel timeout
            
            result = detector.detect("Hello world")
            print(f"   ✅ Basic detection works: {result}")
            
            return {
                "name": "UnifiedLanguageDetector",
                "working": True,
                "features": ["unified_detection"]
            }
            
        except TimeoutError:
            signal.alarm(0)
            print(f"   ❌ Timed out during creation")
            return {"name": "UnifiedLanguageDetector", "working": False, "error": "Timeout"}
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "UnifiedLanguageDetector", "working": False, "error": str(e)}

def test_advanced_detector():
    """Test AdvancedLanguageDetector."""
    print("\n🧪 Testing AdvancedLanguageDetector...")
    
    try:
        # Try with timeout to prevent hanging
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("AdvancedLanguageDetector import timed out")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10 second timeout
        
        try:
            from deep_research_core.utils.advanced_language_detector import AdvancedLanguageDetector
            signal.alarm(0)  # Cancel timeout
            
            config = {'use_fasttext': False}
            detector = AdvancedLanguageDetector(config)
            
            result = detector.detect_language("Hello world")
            print(f"   ✅ Basic detection works: {result}")
            
            return {
                "name": "AdvancedLanguageDetector",
                "working": True,
                "features": ["multi_strategy", "caching", "data_augmentation"]
            }
            
        except TimeoutError:
            signal.alarm(0)
            print(f"   ❌ Timed out during import")
            return {"name": "AdvancedLanguageDetector", "working": False, "error": "Import timeout"}
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {"name": "AdvancedLanguageDetector", "working": False, "error": str(e)}

def main():
    """Main test function."""
    print("🚀 Starting Comprehensive Language Detector Analysis...\n")
    
    results = []
    
    # Test all detectors and utilities
    results.append(test_working_detector())
    results.append(test_multilingual_utils())
    results.append(test_vietnamese_utils())
    results.append(test_simple_detector())
    results.append(test_unified_detector())
    results.append(test_advanced_detector())
    
    # Analysis and recommendations
    print("\n" + "="*80)
    print("📊 COMPREHENSIVE ANALYSIS")
    print("="*80)
    
    working_modules = [r for r in results if r.get("working", False)]
    failed_modules = [r for r in results if not r.get("working", False)]
    
    print(f"\n✅ Working Modules ({len(working_modules)}):")
    for module in working_modules:
        features = ", ".join(module.get("features", []))
        accuracy = module.get("accuracy", "N/A")
        if accuracy != "N/A":
            print(f"   • {module['name']}: {accuracy:.1f}% accuracy - {features}")
        else:
            print(f"   • {module['name']}: {features}")
    
    print(f"\n❌ Failed Modules ({len(failed_modules)}):")
    for module in failed_modules:
        error = module.get("error", "Unknown error")
        print(f"   • {module['name']}: {error}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if any(m["name"] == "WorkingLanguageDetector" and m.get("working") for m in results):
        print("   🥇 PRIMARY: Use WorkingLanguageDetector as main detector")
        print("      - Reliable and fast")
        print("      - Good Vietnamese detection")
        print("      - No external dependencies")
    
    if any(m["name"] == "multilingual_utils" and m.get("working") for m in results):
        print("   🔧 UTILITIES: Use multilingual_utils for text processing")
        print("      - Language detection wrapper")
        print("      - Text normalization")
        print("      - Prompt adaptation")
    
    if any(m["name"] == "vietnamese_utils" and m.get("working") for m in results):
        print("   🇻🇳 VIETNAMESE: Use vietnamese_utils for Vietnamese-specific tasks")
        print("      - Tone removal")
        print("      - Text improvement")
        print("      - Phrase extraction")
    
    # Integration strategy
    print(f"\n🏗️ INTEGRATION STRATEGY:")
    print("   1. Create LanguageDetectorManager")
    print("   2. Use WorkingLanguageDetector as primary")
    print("   3. Integrate vietnamese_utils for Vietnamese enhancement")
    print("   4. Use multilingual_utils for general text processing")
    print("   5. Deprecate/fix failed modules")
    
    print(f"\n🎯 NEXT STEPS:")
    print("   1. Implement LanguageDetectorManager")
    print("   2. Create unified API")
    print("   3. Add comprehensive tests")
    print("   4. Update documentation")
    
    print(f"\n🎉 Analysis completed!")

if __name__ == "__main__":
    main()
