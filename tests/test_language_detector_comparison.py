#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script so sánh các language detector.
"""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_working_detector():
    """Test WorkingLanguageDetector."""
    print("🧪 Testing WorkingLanguageDetector...")
    
    try:
        from deep_research_core.utils.working_language_detector import WorkingLanguageDetector
        
        detector = WorkingLanguageDetector()
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("Đ<PERSON>y là văn bản tiếng Việt", "vi"),
            ("This is an English text", "en"),
            ("Bonjour le monde", "fr"),
            ("Hola mundo", "es"),
            ("Ciao mondo", "it"),
            ("Olá mundo", "pt"),
            ("Hallo Welt", "de"),
            ("Привет мир", "ru"),
        ]
        
        correct = 0
        total = len(test_cases)
        
        start_time = time.time()
        
        for text, expected in test_cases:
            result = detector.detect(text)
            if result == expected:
                correct += 1
                print(f"✅ '{text}' -> {result} (expected: {expected})")
            else:
                print(f"❌ '{text}' -> {result} (expected: {expected})")
        
        end_time = time.time()
        
        accuracy = correct / total * 100
        speed = total / (end_time - start_time)
        
        print(f"\n📊 WorkingLanguageDetector Results:")
        print(f"   Accuracy: {accuracy:.1f}% ({correct}/{total})")
        print(f"   Speed: {speed:.1f} detections/second")
        
        return {
            "name": "WorkingLanguageDetector",
            "accuracy": accuracy,
            "speed": speed,
            "correct": correct,
            "total": total,
            "working": True
        }
        
    except Exception as e:
        print(f"❌ WorkingLanguageDetector failed: {e}")
        return {
            "name": "WorkingLanguageDetector",
            "accuracy": 0,
            "speed": 0,
            "correct": 0,
            "total": 0,
            "working": False,
            "error": str(e)
        }

def test_unified_detector():
    """Test UnifiedLanguageDetector."""
    print("\n🧪 Testing UnifiedLanguageDetector...")
    
    try:
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        
        # Try to create with minimal settings
        detector = UnifiedLanguageDetector(
            use_external_libraries=False,
            enable_cache=False,
            resources_dir=None
        )
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("Đây là văn bản tiếng Việt", "vi"),
            ("This is an English text", "en"),
        ]
        
        correct = 0
        total = len(test_cases)
        
        start_time = time.time()
        
        for text, expected in test_cases:
            result = detector.detect(text)
            if result == expected:
                correct += 1
                print(f"✅ '{text}' -> {result} (expected: {expected})")
            else:
                print(f"❌ '{text}' -> {result} (expected: {expected})")
        
        end_time = time.time()
        
        accuracy = correct / total * 100
        speed = total / (end_time - start_time)
        
        print(f"\n📊 UnifiedLanguageDetector Results:")
        print(f"   Accuracy: {accuracy:.1f}% ({correct}/{total})")
        print(f"   Speed: {speed:.1f} detections/second")
        
        return {
            "name": "UnifiedLanguageDetector",
            "accuracy": accuracy,
            "speed": speed,
            "correct": correct,
            "total": total,
            "working": True
        }
        
    except Exception as e:
        print(f"❌ UnifiedLanguageDetector failed: {e}")
        return {
            "name": "UnifiedLanguageDetector",
            "accuracy": 0,
            "speed": 0,
            "correct": 0,
            "total": 0,
            "working": False,
            "error": str(e)
        }

def test_simple_detector():
    """Test SimpleLanguageDetector."""
    print("\n🧪 Testing SimpleLanguageDetector...")
    
    try:
        from deep_research_core.utils.simple_language_detector import SimpleLanguageDetector
        
        detector = SimpleLanguageDetector()
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("Đây là văn bản tiếng Việt", "vi"),
            ("This is an English text", "en"),
        ]
        
        correct = 0
        total = len(test_cases)
        
        start_time = time.time()
        
        for text, expected in test_cases:
            result = detector.detect(text)
            if result == expected:
                correct += 1
                print(f"✅ '{text}' -> {result} (expected: {expected})")
            else:
                print(f"❌ '{text}' -> {result} (expected: {expected})")
        
        end_time = time.time()
        
        accuracy = correct / total * 100
        speed = total / (end_time - start_time)
        
        print(f"\n📊 SimpleLanguageDetector Results:")
        print(f"   Accuracy: {accuracy:.1f}% ({correct}/{total})")
        print(f"   Speed: {speed:.1f} detections/second")
        
        return {
            "name": "SimpleLanguageDetector",
            "accuracy": accuracy,
            "speed": speed,
            "correct": correct,
            "total": total,
            "working": True
        }
        
    except Exception as e:
        print(f"❌ SimpleLanguageDetector failed: {e}")
        return {
            "name": "SimpleLanguageDetector",
            "accuracy": 0,
            "speed": 0,
            "correct": 0,
            "total": 0,
            "working": False,
            "error": str(e)
        }

def main():
    """Main comparison function."""
    print("🚀 Starting Language Detector Comparison...\n")
    
    results = []
    
    # Test all detectors
    results.append(test_working_detector())
    results.append(test_unified_detector())
    results.append(test_simple_detector())
    
    # Summary
    print("\n" + "="*60)
    print("📊 COMPARISON SUMMARY")
    print("="*60)
    
    working_detectors = [r for r in results if r["working"]]
    
    if working_detectors:
        print("\n🏆 Working Detectors:")
        for result in working_detectors:
            print(f"   {result['name']}: {result['accuracy']:.1f}% accuracy, {result['speed']:.1f} det/sec")
        
        # Find best detector
        best_accuracy = max(working_detectors, key=lambda x: x["accuracy"])
        best_speed = max(working_detectors, key=lambda x: x["speed"])
        
        print(f"\n🥇 Best Accuracy: {best_accuracy['name']} ({best_accuracy['accuracy']:.1f}%)")
        print(f"🚀 Fastest: {best_speed['name']} ({best_speed['speed']:.1f} det/sec)")
        
        # Recommendation
        if best_accuracy["name"] == "WorkingLanguageDetector":
            print(f"\n💡 Recommendation: Use WorkingLanguageDetector for production")
            print("   - Reliable and fast")
            print("   - Good Vietnamese detection")
            print("   - No external dependencies")
        
    else:
        print("\n❌ No working detectors found!")
    
    # Failed detectors
    failed_detectors = [r for r in results if not r["working"]]
    if failed_detectors:
        print(f"\n❌ Failed Detectors:")
        for result in failed_detectors:
            print(f"   {result['name']}: {result.get('error', 'Unknown error')}")
    
    print("\n🎉 Comparison completed!")

if __name__ == "__main__":
    main()
