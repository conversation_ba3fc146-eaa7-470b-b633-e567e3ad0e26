#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho OptimizedLanguageDetector.
"""

import sys
import time
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_basic_functionality():
    """Test basic functionality."""
    print("🧪 Testing OptimizedLanguageDetector basic functionality...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        
        # Create detector
        detector = OptimizedLanguageDetector()
        print("   ✅ Detector created successfully")
        
        # Test basic detection
        lang = detector.detect("Hello world")
        print(f"   'Hello world' -> {lang}")
        
        lang = detector.detect("Xin chào thế giới")
        print(f"   'Xin chào thế giới' -> {lang}")
        
        # Test with confidence
        lang, conf = detector.detect_with_confidence("Hello world")
        print(f"   'Hello world' -> {lang} (confidence: {conf:.2f})")
        
        lang, conf = detector.detect_with_confidence("Xin chào thế giới")
        print(f"   'Xin chào thế giới' -> {lang} (confidence: {conf:.2f})")
        
        print("   ✅ Basic functionality works!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detection_methods():
    """Test different detection methods."""
    print("\n🧪 Testing detection methods...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import (
            OptimizedLanguageDetector, DetectionMethod
        )
        
        detector = OptimizedLanguageDetector()
        
        test_texts = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("Bonjour le monde", "fr"),
            ("Hola mundo", "es"),
        ]
        
        methods = [
            DetectionMethod.CHARACTER_BASED,
            DetectionMethod.WORD_BASED,
            DetectionMethod.HYBRID
        ]
        
        for method in methods:
            print(f"\n   📝 Testing {method.value} method:")
            correct = 0
            
            for text, expected in test_texts:
                lang, conf = detector.detect_with_confidence(text, method)
                is_correct = lang == expected
                if is_correct:
                    correct += 1
                
                status = "✅" if is_correct else "❌"
                print(f"      {status} '{text}' -> {lang} (conf: {conf:.2f}, expected: {expected})")
            
            accuracy = correct / len(test_texts) * 100
            print(f"      📊 Accuracy: {accuracy:.1f}%")
        
        print("   ✅ All detection methods work!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """Test performance."""
    print("\n🧪 Testing performance...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        
        detector = OptimizedLanguageDetector()
        
        test_texts = [
            "Xin chào thế giới",
            "Hello world",
            "Bonjour le monde",
            "Hola mundo",
            "Ciao mondo"
        ]
        
        # Test performance
        iterations = 1000
        start_time = time.time()
        
        for i in range(iterations):
            text = test_texts[i % len(test_texts)]
            detector.detect(text)
        
        end_time = time.time()
        
        total_time = end_time - start_time
        detections_per_second = iterations / total_time
        
        print(f"   📊 Performance: {detections_per_second:.1f} detections/second")
        print(f"   📊 Average time: {(total_time / iterations) * 1000:.2f} ms per detection")
        
        # Test caching effectiveness
        print("\n   🔄 Testing caching...")
        
        # First run (cache miss)
        start_time = time.time()
        for _ in range(100):
            detector.detect("Xin chào thế giới")
        first_run_time = time.time() - start_time
        
        # Second run (cache hit)
        start_time = time.time()
        for _ in range(100):
            detector.detect("Xin chào thế giới")
        second_run_time = time.time() - start_time
        
        if second_run_time < first_run_time:
            speedup = first_run_time / second_run_time
            print(f"   🚀 Caching speedup: {speedup:.1f}x")
        
        # Get stats
        stats = detector.get_stats()
        print(f"   📊 Cache hit rate: {stats['cache_hit_rate']:.1%}")
        print(f"   📊 Success rate: {stats['success_rate']:.1%}")
        
        print("   ✅ Performance test completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thread_safety():
    """Test thread safety."""
    print("\n🧪 Testing thread safety...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        
        detector = OptimizedLanguageDetector()
        
        results = []
        errors = []
        
        def worker_thread(thread_id):
            try:
                for i in range(100):
                    text = f"Hello world {thread_id}-{i}"
                    lang = detector.detect(text)
                    results.append((thread_id, lang))
            except Exception as e:
                errors.append((thread_id, str(e)))
        
        # Create and start threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        print(f"   📊 Total results: {len(results)}")
        print(f"   📊 Total errors: {len(errors)}")
        
        if len(errors) == 0:
            print("   ✅ Thread safety test passed!")
            return True
        else:
            print("   ❌ Thread safety test failed!")
            for thread_id, error in errors:
                print(f"      Thread {thread_id}: {error}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing convenience functions...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import (
            detect_language_optimized, detect_language_with_confidence_optimized,
            is_vietnamese_optimized, is_english_optimized, get_optimized_language_detector
        )
        
        # Test detect_language_optimized
        lang = detect_language_optimized("Hello world")
        print(f"   detect_language_optimized('Hello world'): {lang}")
        
        # Test detect_language_with_confidence_optimized
        lang, conf = detect_language_with_confidence_optimized("Xin chào")
        print(f"   detect_language_with_confidence_optimized('Xin chào'): {lang} (conf: {conf:.2f})")
        
        # Test is_vietnamese_optimized
        is_vi = is_vietnamese_optimized("Xin chào thế giới")
        print(f"   is_vietnamese_optimized('Xin chào thế giới'): {is_vi}")
        
        # Test is_english_optimized
        is_en = is_english_optimized("Hello world")
        print(f"   is_english_optimized('Hello world'): {is_en}")
        
        # Test global detector
        detector = get_optimized_language_detector()
        supported_langs = detector.get_supported_languages()
        print(f"   Supported languages: {len(supported_langs)}")
        
        print("   ✅ All convenience functions work!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases."""
    print("\n🧪 Testing edge cases...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        
        detector = OptimizedLanguageDetector()
        
        edge_cases = [
            ("", "Empty string"),
            ("Hi", "Very short text"),
            ("123", "Numbers only"),
            ("!@#$%", "Symbols only"),
            ("   ", "Whitespace only"),
            ("Hello xin chào world thế giới", "Mixed languages"),
            ("A" * 1000, "Very long text"),
        ]
        
        for text, description in edge_cases:
            try:
                lang, conf = detector.detect_with_confidence(text)
                print(f"   ✅ {description}: '{text[:20]}...' -> {lang} (conf: {conf:.2f})")
            except Exception as e:
                print(f"   ❌ {description}: Error - {e}")
                return False
        
        print("   ✅ All edge cases handled correctly!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting OptimizedLanguageDetector tests...\n")
    
    tests = [
        test_basic_functionality,
        test_detection_methods,
        test_performance,
        test_thread_safety,
        test_convenience_functions,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n" + "="*80)
    print(f"📊 TEST RESULTS")
    print(f"="*80)
    print(f"✅ Passed: {passed}/{total} tests")
    print(f"📈 Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n🎉 All tests passed! OptimizedLanguageDetector is production-ready!")
        print(f"\n💡 Key Features Verified:")
        print(f"   ✅ High-performance detection")
        print(f"   ✅ Multiple detection methods")
        print(f"   ✅ Thread-safe operations")
        print(f"   ✅ Intelligent caching")
        print(f"   ✅ Vietnamese optimization")
        print(f"   ✅ Comprehensive statistics")
        print(f"   ✅ Edge case handling")
        
        print(f"\n🚀 Ready for production deployment!")
        return True
    else:
        print(f"\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
