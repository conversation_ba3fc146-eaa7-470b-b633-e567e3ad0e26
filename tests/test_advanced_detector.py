#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho AdvancedLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_advanced_detector():
    """Test AdvancedLanguageDetector."""
    print("🧪 Testing AdvancedLanguageDetector...")
    
    try:
        from deep_research_core.utils.advanced_language_detector import AdvancedLanguageDetector
        
        # Create detector with minimal config
        config = {
            'supported_languages': ['vi', 'en', 'fr', 'de', 'es'],
            'min_length': 5,
            'default_language': 'en',
            'confidence_threshold': 0.3,
            'use_fasttext': False  # Disable fasttext to avoid model loading
        }
        
        detector = AdvancedLanguageDetector(config)
        print("✅ AdvancedLanguageDetector created successfully")
        
        # Test Vietnamese detection
        vietnamese_texts = [
            "Xin chào thế giới",
            "<PERSON><PERSON><PERSON> là văn bản tiếng Việt",
            "Tôi có thể nói tiếng Việt",
            "Hôm nay trời đẹp",
            "Chúng tôi đang học tiếng Việt"
        ]
        
        print("\n📝 Testing Vietnamese detection:")
        vi_correct = 0
        for text in vietnamese_texts:
            lang, conf = detector.detect_language(text)
            if lang == "vi":
                vi_correct += 1
                print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
            else:
                print(f"  ❌ '{text}' -> {lang} (confidence: {conf:.2f})")
        
        # Test English detection
        english_texts = [
            "Hello world",
            "This is an English text",
            "I can speak English",
            "Today is a beautiful day",
            "We are learning English"
        ]
        
        print("\n📝 Testing English detection:")
        en_correct = 0
        for text in english_texts:
            lang, conf = detector.detect_language(text)
            if lang == "en":
                en_correct += 1
                print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
            else:
                print(f"  ❌ '{text}' -> {lang} (confidence: {conf:.2f})")
        
        # Test other languages
        other_tests = [
            ("Bonjour le monde", "fr"),
            ("Hola mundo", "es"),
            ("Hallo Welt", "de"),
        ]
        
        print("\n📝 Testing other languages:")
        other_correct = 0
        for text, expected in other_tests:
            lang, conf = detector.detect_language(text)
            if lang == expected:
                other_correct += 1
                print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
            else:
                print(f"  ❌ '{text}' -> {lang} (expected: {expected}, confidence: {conf:.2f})")
        
        # Test utility methods
        print("\n📝 Testing utility methods:")
        assert detector.is_vietnamese("Xin chào"), "Should detect Vietnamese"
        print("  ✅ is_vietnamese() works correctly")
        
        # Test edge cases
        print("\n📝 Testing edge cases:")
        
        # Short text
        lang, conf = detector.detect_language("Hi")
        print(f"  Short text 'Hi': {lang} (confidence: {conf:.2f})")
        
        # Empty text
        lang, conf = detector.detect_language("")
        print(f"  Empty text: {lang} (confidence: {conf:.2f})")
        
        # Mixed language
        mixed_text = "Hello xin chào world thế giới"
        lang, conf = detector.detect_language(mixed_text)
        print(f"  Mixed text: {lang} (confidence: {conf:.2f})")
        
        # Summary
        total_tests = len(vietnamese_texts) + len(english_texts) + len(other_tests)
        total_correct = vi_correct + en_correct + other_correct
        accuracy = total_correct / total_tests * 100
        
        print(f"\n📊 Test Results:")
        print(f"   Vietnamese: {vi_correct}/{len(vietnamese_texts)} correct")
        print(f"   English: {en_correct}/{len(english_texts)} correct")
        print(f"   Other: {other_correct}/{len(other_tests)} correct")
        print(f"   Overall Accuracy: {accuracy:.1f}% ({total_correct}/{total_tests})")
        
        if accuracy >= 60:  # Lower threshold for advanced detector
            print(f"\n🎉 AdvancedLanguageDetector works well!")
            return True
        else:
            print(f"\n❌ AdvancedLanguageDetector needs improvement")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing convenience functions...")
    
    try:
        from deep_research_core.utils.advanced_language_detector import detect_language, is_vietnamese
        
        # Test detect_language function
        lang, conf = detect_language("Hello world")
        print(f"✅ detect_language function: {lang} (confidence: {conf:.2f})")
        
        # Test is_vietnamese function
        result = is_vietnamese("Xin chào")
        print(f"✅ is_vietnamese function: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_augmentation():
    """Test data augmentation feature."""
    print("\n🧪 Testing data augmentation...")
    
    try:
        from deep_research_core.utils.advanced_language_detector import AdvancedLanguageDetector
        
        detector = AdvancedLanguageDetector()
        
        # Test simple data
        data = {
            "title": "Xin chào thế giới",
            "content": "Hello world",
            "description": "Bonjour le monde"
        }
        
        augmented_data = detector.augment_with_language_info(data)
        print(f"✅ Data augmentation completed")
        
        if "metadata" in augmented_data:
            print(f"   Metadata added: {augmented_data['metadata']}")
        
        # Test structured data
        structured_data = {
            "results": [
                {
                    "title": "Xin chào",
                    "snippet": "Đây là văn bản tiếng Việt"
                },
                {
                    "title": "Hello",
                    "snippet": "This is English text"
                }
            ]
        }
        
        augmented_structured = detector.augment_with_language_info(structured_data)
        print(f"✅ Structured data augmentation completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting AdvancedLanguageDetector tests...\n")
    
    success1 = test_advanced_detector()
    success2 = test_convenience_functions()
    success3 = test_data_augmentation()
    
    if success1 and success2 and success3:
        print("\n🎉 All AdvancedLanguageDetector tests passed!")
        print("\n💡 AdvancedLanguageDetector Features:")
        print("   - Multi-strategy detection (char patterns + vocabulary + external libs)")
        print("   - Caching for performance")
        print("   - Data augmentation capabilities")
        print("   - Support for multiple languages")
        print("   - Configurable thresholds and settings")
        return True
    else:
        print("\n❌ Some AdvancedLanguageDetector tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
