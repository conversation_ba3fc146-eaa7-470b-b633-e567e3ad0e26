#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Production-ready system test cho Language Detector.

Test toàn bộ hệ thống với production configurations và scenarios.
"""

import sys
import time
import threading
import concurrent.futures
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_optimized_detector_production():
    """Test OptimizedLanguageDetector với production config."""
    print("🧪 Testing OptimizedLanguageDetector (Production Config)...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        from deep_research_core.utils.language_detector_config import get_predefined_config
        
        # Production configuration
        config = get_predefined_config('prod_balanced')
        detector = OptimizedLanguageDetector(config)
        
        # Test cases
        test_cases = [
            ("Xin chào thế giới", "vi"),
            ("Hello world", "en"),
            ("<PERSON><PERSON><PERSON> le monde", "fr"),
            ("Hola mundo", "es"),
            ("Ciao mondo", "it"),
            ("Olá mundo", "pt"),
            ("Hallo Welt", "de"),
            ("Привет мир", "ru"),
        ]
        
        correct = 0
        total_time = 0
        
        for text, expected in test_cases:
            start_time = time.time()
            lang, conf = detector.detect_with_confidence(text)
            elapsed = time.time() - start_time
            total_time += elapsed
            
            if lang == expected:
                correct += 1
                print(f"   ✅ '{text}' -> {lang} (conf: {conf:.2f}, time: {elapsed*1000:.1f}ms)")
            else:
                print(f"   ❌ '{text}' -> {lang} (expected: {expected}, conf: {conf:.2f})")
        
        accuracy = correct / len(test_cases) * 100
        avg_time = (total_time / len(test_cases)) * 1000
        
        print(f"   📊 Accuracy: {accuracy:.1f}%")
        print(f"   📊 Average time: {avg_time:.2f}ms")
        
        # Get stats
        stats = detector.get_stats()
        print(f"   📊 Success rate: {stats['success_rate']:.1%}")
        print(f"   📊 Cache hit rate: {stats['cache_hit_rate']:.1%}")
        
        return accuracy >= 80 and avg_time < 10  # Production requirements
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_high_volume_performance():
    """Test high-volume performance."""
    print("\n🧪 Testing high-volume performance...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        from deep_research_core.utils.language_detector_config import get_use_case_config
        
        # High-traffic configuration
        config = get_use_case_config('web_app_high_traffic')
        detector = OptimizedLanguageDetector(config)
        
        test_texts = [
            "Xin chào thế giới",
            "Hello world",
            "Bonjour le monde",
            "Hola mundo",
            "Ciao mondo"
        ]
        
        # High-volume test
        iterations = 10000
        start_time = time.time()
        
        for i in range(iterations):
            text = test_texts[i % len(test_texts)]
            detector.detect(text)
        
        end_time = time.time()
        
        total_time = end_time - start_time
        detections_per_second = iterations / total_time
        
        print(f"   📊 Performance: {detections_per_second:.0f} detections/second")
        print(f"   📊 Total time: {total_time:.2f}s for {iterations} detections")
        
        # Check cache effectiveness
        stats = detector.get_stats()
        print(f"   📊 Cache hit rate: {stats['cache_hit_rate']:.1%}")
        print(f"   📊 Success rate: {stats['success_rate']:.1%}")
        
        # Production requirement: > 1000 detections/second
        return detections_per_second > 1000
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_access():
    """Test concurrent access (thread safety)."""
    print("\n🧪 Testing concurrent access...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        from deep_research_core.utils.language_detector_config import get_predefined_config
        
        config = get_predefined_config('prod_balanced')
        detector = OptimizedLanguageDetector(config)
        
        results = []
        errors = []
        
        def worker_task(worker_id, num_requests):
            try:
                for i in range(num_requests):
                    text = f"Hello world from worker {worker_id} request {i}"
                    lang = detector.detect(text)
                    results.append((worker_id, i, lang))
            except Exception as e:
                errors.append((worker_id, str(e)))
        
        # Test với multiple threads
        num_workers = 10
        requests_per_worker = 100
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(worker_task, worker_id, requests_per_worker)
                for worker_id in range(num_workers)
            ]
            
            concurrent.futures.wait(futures)
        
        end_time = time.time()
        
        total_requests = num_workers * requests_per_worker
        total_time = end_time - start_time
        
        print(f"   📊 Total requests: {total_requests}")
        print(f"   📊 Successful results: {len(results)}")
        print(f"   📊 Errors: {len(errors)}")
        print(f"   📊 Total time: {total_time:.2f}s")
        print(f"   📊 Requests/second: {total_requests/total_time:.0f}")
        
        # Check for errors
        if errors:
            print("   ❌ Errors found:")
            for worker_id, error in errors[:5]:  # Show first 5 errors
                print(f"      Worker {worker_id}: {error}")
        
        # Production requirement: no errors, > 500 requests/second
        return len(errors) == 0 and (total_requests/total_time) > 500
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_usage():
    """Test memory usage."""
    print("\n🧪 Testing memory usage...")
    
    try:
        import psutil
        import os
        
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        from deep_research_core.utils.language_detector_config import get_use_case_config
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create detector với large cache
        config = get_use_case_config('web_app_high_traffic')
        detector = OptimizedLanguageDetector(config)
        
        # Fill cache với many different texts
        for i in range(5000):
            text = f"This is test text number {i} for memory testing"
            detector.detect(text)
        
        # Check memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"   📊 Initial memory: {initial_memory:.1f} MB")
        print(f"   📊 Final memory: {final_memory:.1f} MB")
        print(f"   📊 Memory increase: {memory_increase:.1f} MB")
        
        # Get cache stats
        stats = detector.get_stats()
        print(f"   📊 Cache size: {stats['cache_size']}")
        
        # Production requirement: memory increase < 50MB
        return memory_increase < 50
        
    except ImportError:
        print("   ⚠️ psutil not available, skipping memory test")
        return True
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_system():
    """Test configuration system."""
    print("\n🧪 Testing configuration system...")
    
    try:
        from deep_research_core.utils.language_detector_config import (
            get_predefined_config, get_use_case_config, create_custom_config,
            validate_config, Environment, PerformanceProfile, get_config
        )
        
        # Test predefined configs
        configs_to_test = ['prod_balanced', 'prod_fast', 'dev_balanced']
        
        for config_name in configs_to_test:
            config = get_predefined_config(config_name)
            validate_config(config)
            print(f"   ✅ {config_name}: valid")
        
        # Test use case configs
        use_cases = ['web_app_high_traffic', 'vietnamese_app', 'microservice_low_memory']
        
        for use_case in use_cases:
            config = get_use_case_config(use_case)
            validate_config(config)
            print(f"   ✅ {use_case}: valid")
        
        # Test custom config
        custom_config = create_custom_config(
            'prod_balanced',
            cache_size=5000,
            min_confidence=0.4
        )
        validate_config(custom_config)
        print(f"   ✅ custom config: valid")
        
        # Test environment/profile configs
        config = get_config(Environment.PRODUCTION, PerformanceProfile.HIGH_SPEED)
        validate_config(config)
        print(f"   ✅ environment/profile config: valid")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vietnamese_optimization():
    """Test Vietnamese optimization."""
    print("\n🧪 Testing Vietnamese optimization...")
    
    try:
        from deep_research_core.utils.optimized_language_detector import OptimizedLanguageDetector
        from deep_research_core.utils.language_detector_config import get_use_case_config
        
        # Vietnamese-optimized configuration
        config = get_use_case_config('vietnamese_app')
        detector = OptimizedLanguageDetector(config)
        
        vietnamese_texts = [
            "Xin chào thế giới",
            "Đây là văn bản tiếng Việt",
            "Tôi có thể nói tiếng Việt",
            "Hôm nay trời đẹp",
            "Chúng tôi đang học tiếng Việt",
            "Việt Nam là một đất nước xinh đẹp",
            "Tôi yêu tiếng Việt của mình",
            "Hà Nội là thủ đô của Việt Nam",
            "Phở là món ăn truyền thống của Việt Nam",
            "Tết Nguyên Đán là ngày lễ quan trọng nhất"
        ]
        
        correct = 0
        total_confidence = 0
        
        for text in vietnamese_texts:
            lang, conf = detector.detect_with_confidence(text)
            if lang == "vi":
                correct += 1
            total_confidence += conf
            
            status = "✅" if lang == "vi" else "❌"
            print(f"   {status} '{text}' -> {lang} (conf: {conf:.2f})")
        
        accuracy = correct / len(vietnamese_texts) * 100
        avg_confidence = total_confidence / len(vietnamese_texts)
        
        print(f"   📊 Vietnamese accuracy: {accuracy:.1f}%")
        print(f"   📊 Average confidence: {avg_confidence:.2f}")
        
        # Production requirement: > 90% accuracy for Vietnamese
        return accuracy > 90
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main production test function."""
    print("🚀 Starting Production-Ready System Tests...\n")
    
    tests = [
        ("Optimized Detector (Production)", test_optimized_detector_production),
        ("High-Volume Performance", test_high_volume_performance),
        ("Concurrent Access", test_concurrent_access),
        ("Memory Usage", test_memory_usage),
        ("Configuration System", test_configuration_system),
        ("Vietnamese Optimization", test_vietnamese_optimization),
    ]
    
    passed = 0
    total = len(tests)
    results = {}
    
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            elapsed = time.time() - start_time
            
            if result:
                passed += 1
                status = "✅ PASSED"
            else:
                status = "❌ FAILED"
            
            results[test_name] = {
                'status': result,
                'time': elapsed
            }
            
            print(f"{status} ({elapsed:.2f}s)\n")
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ ERROR: {e} ({elapsed:.2f}s)\n")
            results[test_name] = {
                'status': False,
                'time': elapsed,
                'error': str(e)
            }
    
    # Summary
    print("="*80)
    print("📊 PRODUCTION READINESS ASSESSMENT")
    print("="*80)
    
    print(f"\n✅ Passed: {passed}/{total} tests")
    print(f"📈 Success rate: {passed/total*100:.1f}%")
    
    print(f"\n📋 Test Results:")
    for test_name, result in results.items():
        status = "✅" if result['status'] else "❌"
        time_str = f"{result['time']:.2f}s"
        print(f"   {status} {test_name}: {time_str}")
        
        if 'error' in result:
            print(f"      Error: {result['error']}")
    
    # Production readiness assessment
    if passed == total:
        print(f"\n🎉 SYSTEM IS PRODUCTION READY!")
        print(f"\n✅ All tests passed successfully")
        print(f"✅ Performance meets production requirements")
        print(f"✅ Thread safety verified")
        print(f"✅ Memory usage within limits")
        print(f"✅ Configuration system working")
        print(f"✅ Vietnamese optimization effective")
        
        print(f"\n🚀 Ready for deployment!")
        
    elif passed >= total * 0.8:  # 80% pass rate
        print(f"\n⚠️ SYSTEM IS MOSTLY READY")
        print(f"✅ {passed} tests passed, {total-passed} failed")
        print(f"⚠️ Some optimizations may be needed")
        print(f"🔧 Review failed tests before deployment")
        
    else:
        print(f"\n❌ SYSTEM NEEDS MORE WORK")
        print(f"❌ Only {passed}/{total} tests passed")
        print(f"🔧 Significant improvements needed")
        print(f"⚠️ Not recommended for production deployment")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
