#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script cho UnifiedLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def debug_language_detection():
    """Debug language detection step by step."""
    print("🔍 Debugging UnifiedLanguageDetector...")

    # Enable debug logging
    import logging
    logging.basicConfig(level=logging.DEBUG)

    try:
        # Import the module
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        print("✅ Import successful")
        
        # Create detector with minimal settings
        detector = UnifiedLanguageDetector(
            use_external_libraries=False,
            enable_cache=False,
            min_confidence=0.3
        )
        print("✅ Detector created")
        
        # Test Vietnamese text
        vietnamese_text = "Xin chào thế giới"
        print(f"\n🧪 Testing Vietnamese text: '{vietnamese_text}'")
        
        # Test character detection
        char_scores = detector._detect_by_chars(vietnamese_text)
        print(f"Character scores: {char_scores}")
        
        # Test word detection
        word_scores = detector._detect_by_common_words(vietnamese_text)
        print(f"Word scores: {word_scores}")
        
        # Test full detection with debug
        result = detector.detect_language(vietnamese_text, return_confidence=True)
        if isinstance(result, tuple):
            lang, conf = result
        else:
            lang, conf = result, 0.0
        print(f"Final result: {lang} (confidence: {conf:.2f})")

        # Test with lower confidence threshold
        detector.min_confidence = 0.1
        lang2, conf2 = detector.detect_language(vietnamese_text, return_confidence=True)
        print(f"With lower threshold: {lang2} (confidence: {conf2:.2f})")
        
        # Test English text
        english_text = "Hello world"
        print(f"\n🧪 Testing English text: '{english_text}'")
        
        # Test character detection
        char_scores = detector._detect_by_chars(english_text)
        print(f"Character scores: {char_scores}")
        
        # Test word detection
        word_scores = detector._detect_by_common_words(english_text)
        print(f"Word scores: {word_scores}")
        
        # Test full detection
        lang, conf = detector.detect_language(english_text, return_confidence=True)
        print(f"Final result: {lang} (confidence: {conf:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_language_detection()
    sys.exit(0 if success else 1)
