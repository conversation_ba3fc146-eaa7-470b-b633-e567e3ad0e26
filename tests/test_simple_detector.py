#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test simple detector functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class SimpleLanguageDetector:
    """Simple language detector for testing."""
    
    def __init__(self):
        self.vietnamese_chars = set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ")
        self.vietnamese_words = {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã"}
        self.english_words = {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with"}
    
    def detect(self, text: str) -> str:
        """Simple detection."""
        if not text:
            return "en"
        
        text_lower = text.lower()
        
        # Count Vietnamese characters
        vi_char_count = sum(1 for char in text_lower if char in self.vietnamese_chars)
        total_chars = len([c for c in text_lower if c.isalpha()])
        
        if total_chars > 0 and vi_char_count / total_chars > 0.1:
            return "vi"
        
        # Count Vietnamese words
        words = text_lower.split()
        vi_word_count = sum(1 for word in words if word in self.vietnamese_words)
        en_word_count = sum(1 for word in words if word in self.english_words)
        
        if vi_word_count > en_word_count:
            return "vi"
        
        return "en"

def test_simple_detector():
    """Test simple detector."""
    print("🧪 Testing SimpleLanguageDetector...")
    
    detector = SimpleLanguageDetector()
    
    # Test Vietnamese
    vietnamese_texts = [
        "Xin chào thế giới",
        "Đây là văn bản tiếng Việt",
        "Tôi có thể nói tiếng Việt",
        "Hôm nay trời đẹp"
    ]
    
    for text in vietnamese_texts:
        result = detector.detect(text)
        print(f"'{text}' -> {result}")
        assert result == "vi", f"Expected 'vi', got {result}"
    
    # Test English
    english_texts = [
        "Hello world",
        "This is an English text",
        "I can speak English",
        "Today is a beautiful day"
    ]
    
    for text in english_texts:
        result = detector.detect(text)
        print(f"'{text}' -> {result}")
        assert result == "en", f"Expected 'en', got {result}"
    
    print("✅ SimpleLanguageDetector works correctly!")
    return True

def test_unified_detector():
    """Test unified detector."""
    print("\n🧪 Testing UnifiedLanguageDetector...")
    
    try:
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        
        # Create with minimal settings
        print("Creating detector...")
        detector = UnifiedLanguageDetector(
            use_external_libraries=False,
            enable_cache=False,
            resources_dir=None,  # Don't try to load files
            supported_languages=["en", "vi"]  # Minimal languages
        )
        print("✅ Detector created")
        
        # Test detection
        result = detector.detect("Hello world")
        print(f"English test: {result}")
        
        result = detector.detect("Xin chào")
        print(f"Vietnamese test: {result}")
        
        print("✅ UnifiedLanguageDetector works!")
        return True
        
    except Exception as e:
        print(f"❌ UnifiedLanguageDetector failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting language detector tests...\n")
    
    success1 = test_simple_detector()
    success2 = test_unified_detector()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
        return True
    else:
        print("\n❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
