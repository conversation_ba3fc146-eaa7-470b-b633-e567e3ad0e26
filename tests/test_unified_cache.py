#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho UnifiedCache system.

Script này test tất cả tính năng của UnifiedCache để đảm bảo
việc consolidation cache utilities thành công.
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from deep_research_core.utils.unified_cache import UnifiedCache, CacheEntry, get_cache, create_cache_key, determine_ttl_by_content_type

def test_basic_cache_operations():
    """Test các operations cơ bản của cache."""
    print("🧪 Testing basic cache operations...")
    
    # Tạo temporary directory cho test
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            default_ttl=60,
            max_memory_size=10,
            cache_dir=temp_dir,
            enable_disk_cache=True,
            enable_redis_cache=False,  # Disable Redis for testing
            enable_semantic_search=False  # Disable semantic search for basic test
        )
        
        # Test set và get
        cache.set("test_key", "test_value", content_type="test")
        result = cache.get("test_key")
        assert result == "test_value", f"Expected 'test_value', got {result}"
        print("✅ Basic set/get works")
        
        # Test với complex data
        complex_data = {
            "list": [1, 2, 3],
            "dict": {"nested": "value"},
            "number": 42
        }
        cache.set("complex_key", complex_data)
        result = cache.get("complex_key")
        assert result == complex_data, f"Complex data mismatch: {result}"
        print("✅ Complex data storage works")
        
        # Test default value
        result = cache.get("nonexistent_key", "default")
        assert result == "default", f"Expected 'default', got {result}"
        print("✅ Default value works")
        
        # Test delete
        cache.set("delete_me", "value")
        assert cache.get("delete_me") == "value"
        cache.delete("delete_me")
        assert cache.get("delete_me") is None
        print("✅ Delete works")
        
        # Test clear
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.clear()
        assert cache.get("key1") is None
        assert cache.get("key2") is None
        print("✅ Clear works")

def test_ttl_functionality():
    """Test TTL (Time To Live) functionality."""
    print("\n🧪 Testing TTL functionality...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            default_ttl=1,  # 1 second TTL
            cache_dir=temp_dir,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Test basic TTL
        cache.set("ttl_key", "ttl_value", ttl=1)
        assert cache.get("ttl_key") == "ttl_value"
        
        # Wait for expiration
        time.sleep(1.5)
        assert cache.get("ttl_key") is None
        print("✅ Basic TTL expiration works")
        
        # Test adaptive TTL
        cache.adaptive_ttl = True
        cache.set("news_key", "news_value", content_type="news")
        cache.set("wiki_key", "wiki_value", content_type="wiki")
        
        # News should have shorter TTL than wiki
        stats = cache.get_stats()
        print(f"✅ Adaptive TTL enabled, cache size: {stats['memory_size']}")

def test_multi_tier_caching():
    """Test multi-tier caching (memory -> disk)."""
    print("\n🧪 Testing multi-tier caching...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            max_memory_size=2,  # Very small memory cache
            cache_dir=temp_dir,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Fill memory cache beyond limit
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.set("key3", "value3")  # This should evict key1 from memory
        
        # key1 should be evicted from memory but available from disk
        result = cache.get("key1")
        assert result == "value1", f"Expected 'value1' from disk, got {result}"
        print("✅ Multi-tier caching works (memory -> disk)")
        
        # Check that it's back in memory now
        stats = cache.get_stats()
        print(f"✅ Cache stats: memory_hits={stats['memory_hits']}, disk_hits={stats['disk_hits']}")

def test_react_style_caching():
    """Test ReAct-style caching với tool_name và tool_args."""
    print("\n🧪 Testing ReAct-style caching...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            cache_dir=temp_dir,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Test ReAct caching
        tool_args = {"query": "test query", "max_results": 10}
        cache.set(
            key="search_result",
            value={"results": ["result1", "result2"]},
            tool_name="web_search",
            tool_args=tool_args
        )
        
        # Retrieve with same tool_name and tool_args
        result = cache.get("search_result", tool_name="web_search", tool_args=tool_args)
        assert result == {"results": ["result1", "result2"]}
        print("✅ ReAct-style caching works")
        
        # Different tool_args should not match
        different_args = {"query": "different query", "max_results": 10}
        result = cache.get("search_result", tool_name="web_search", tool_args=different_args)
        assert result is None
        print("✅ ReAct cache key differentiation works")

def test_compression():
    """Test compression functionality."""
    print("\n🧪 Testing compression...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            cache_dir=temp_dir,
            enable_compression=True,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Test with large data that should benefit from compression
        large_data = "x" * 1000  # 1KB of repeated data
        cache.set("large_key", large_data)
        
        result = cache.get("large_key")
        assert result == large_data
        
        stats = cache.get_stats()
        if stats.get("compressions", 0) > 0:
            print("✅ Compression is working")
        else:
            print("⚠️ Compression not triggered (data may not be compressible enough)")

def test_statistics():
    """Test cache statistics."""
    print("\n🧪 Testing statistics...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            cache_dir=temp_dir,
            enable_statistics=True,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Generate some cache activity
        cache.set("stat_key1", "value1")
        cache.set("stat_key2", "value2")
        cache.get("stat_key1")  # Hit
        cache.get("stat_key2")  # Hit
        cache.get("nonexistent")  # Miss
        
        stats = cache.get_stats()
        assert stats["memory_hits"] >= 2
        assert stats["misses"] >= 1
        assert stats["sets"] >= 2
        
        print(f"✅ Statistics working: {stats}")

def test_convenience_functions():
    """Test convenience functions for backward compatibility."""
    print("\n🧪 Testing convenience functions...")
    
    # Test create_cache_key
    key1 = create_cache_key("prefix", "arg1", "arg2", param1="value1")
    key2 = create_cache_key("prefix", "arg1", "arg2", param1="value1")
    key3 = create_cache_key("prefix", "arg1", "arg2", param1="value2")
    
    assert key1 == key2, "Same arguments should produce same key"
    assert key1 != key3, "Different arguments should produce different keys"
    print("✅ create_cache_key works")
    
    # Test determine_ttl_by_content_type
    news_ttl = determine_ttl_by_content_type("news")
    wiki_ttl = determine_ttl_by_content_type("wiki")
    
    assert news_ttl < wiki_ttl, "News should have shorter TTL than wiki"
    print("✅ determine_ttl_by_content_type works")
    
    # Test get_cache (global instance)
    global_cache = get_cache()
    assert isinstance(global_cache, UnifiedCache)
    print("✅ get_cache works")

def test_error_handling():
    """Test error handling và edge cases."""
    print("\n🧪 Testing error handling...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache = UnifiedCache(
            cache_dir=temp_dir,
            enable_disk_cache=True,
            enable_redis_cache=False
        )
        
        # Test với None values
        cache.set("none_key", None)
        result = cache.get("none_key")
        assert result is None
        print("✅ None value handling works")
        
        # Test với empty strings
        cache.set("empty_key", "")
        result = cache.get("empty_key")
        assert result == ""
        print("✅ Empty string handling works")
        
        # Test với invalid cache directory (should handle gracefully)
        try:
            invalid_cache = UnifiedCache(
                cache_dir="/invalid/path/that/does/not/exist",
                enable_disk_cache=True,
                enable_redis_cache=False
            )
            # Should still work with memory cache only
            invalid_cache.set("test", "value")
            result = invalid_cache.get("test")
            print("✅ Invalid cache directory handled gracefully")
        except Exception as e:
            print(f"⚠️ Error handling could be improved: {e}")

def run_all_tests():
    """Chạy tất cả tests."""
    print("🚀 Starting UnifiedCache tests...\n")
    
    try:
        test_basic_cache_operations()
        test_ttl_functionality()
        test_multi_tier_caching()
        test_react_style_caching()
        test_compression()
        test_statistics()
        test_convenience_functions()
        test_error_handling()
        
        print("\n🎉 All tests passed! UnifiedCache is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
