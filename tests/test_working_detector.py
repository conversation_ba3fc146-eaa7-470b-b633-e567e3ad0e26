#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho WorkingLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_working_detector():
    """Test WorkingLanguageDetector."""
    print("🧪 Testing WorkingLanguageDetector...")
    
    try:
        from deep_research_core.utils.working_language_detector import WorkingLanguageDetector
        
        # Create detector
        detector = WorkingLanguageDetector()
        print("✅ Detector created successfully")
        
        # Test Vietnamese detection
        vietnamese_texts = [
            "Xin chào thế giới",
            "Đây là văn bản tiếng Việt",
            "Tôi có thể nói tiếng Việt",
            "Hôm nay trời đẹp",
            "Chúng tôi đang học tiếng Việt"
        ]
        
        print("\n📝 Testing Vietnamese detection:")
        for text in vietnamese_texts:
            lang, conf = detector.detect_language(text, return_confidence=True)
            print(f"  '{text}' -> {lang} (confidence: {conf:.2f})")
            assert lang == "vi", f"Expected 'vi', got {lang}"
        
        # Test English detection
        english_texts = [
            "Hello world",
            "This is an English text",
            "I can speak English",
            "Today is a beautiful day",
            "We are learning English"
        ]
        
        print("\n📝 Testing English detection:")
        for text in english_texts:
            lang, conf = detector.detect_language(text, return_confidence=True)
            print(f"  '{text}' -> {lang} (confidence: {conf:.2f})")
            assert lang == "en", f"Expected 'en', got {lang}"
        
        # Test utility methods
        print("\n📝 Testing utility methods:")
        assert detector.is_vietnamese("Xin chào"), "Should detect Vietnamese"
        assert detector.is_english("Hello world"), "Should detect English"
        assert not detector.is_vietnamese("Hello world"), "Should not detect Vietnamese for English"
        assert not detector.is_english("Xin chào"), "Should not detect English for Vietnamese"
        print("✅ Utility methods work correctly")
        
        # Test simple detect method
        print("\n📝 Testing simple detect method:")
        assert detector.detect("Xin chào") == "vi", "Should detect Vietnamese"
        assert detector.detect("Hello world") == "en", "Should detect English"
        print("✅ Simple detect method works correctly")
        
        # Test stats
        stats = detector.get_stats()
        print(f"\n📊 Detector stats: {stats}")
        assert stats["type"] == "WorkingLanguageDetector", "Should have correct type"
        
        print("\n🎉 WorkingLanguageDetector works perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing convenience functions...")
    
    try:
        from deep_research_core.utils.working_language_detector import (
            detect_language, is_vietnamese, is_english, get_working_language_detector
        )
        
        # Test detect_language function
        lang, conf = detect_language("Hello world")
        assert lang == "en", f"Expected 'en', got {lang}"
        print(f"✅ detect_language function: {lang} (confidence: {conf:.2f})")
        
        # Test is_vietnamese function
        assert is_vietnamese("Xin chào"), "Should detect Vietnamese"
        assert not is_vietnamese("Hello"), "Should not detect Vietnamese for English"
        print("✅ is_vietnamese function works")
        
        # Test is_english function
        assert is_english("Hello world"), "Should detect English"
        assert not is_english("Xin chào"), "Should not detect English for Vietnamese"
        print("✅ is_english function works")
        
        # Test global detector
        global_detector = get_working_language_detector()
        assert global_detector is not None, "Should return detector instance"
        
        # Test that it returns the same instance
        global_detector2 = get_working_language_detector()
        assert global_detector is global_detector2, "Should return same instance"
        print("✅ Global detector works")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases."""
    print("\n🧪 Testing edge cases...")
    
    try:
        from deep_research_core.utils.working_language_detector import WorkingLanguageDetector
        
        detector = WorkingLanguageDetector()
        
        # Test empty text
        lang = detector.detect("")
        assert lang == "en", "Should fallback to default for empty text"
        print("✅ Empty text handled correctly")
        
        # Test short text
        lang = detector.detect("Hi")
        assert lang == "en", "Should fallback to default for short text"
        print("✅ Short text handled correctly")
        
        # Test mixed language text
        mixed_text = "Hello xin chào world thế giới"
        lang, conf = detector.detect_language(mixed_text, return_confidence=True)
        print(f"✅ Mixed text: {lang} (confidence: {conf:.2f})")
        
        # Test text with numbers and punctuation
        punct_text = "Hello, world! 123 test."
        lang = detector.detect(punct_text)
        assert lang == "en", "Should detect English despite punctuation"
        print("✅ Punctuation text handled correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting WorkingLanguageDetector tests...\n")
    
    success1 = test_working_detector()
    success2 = test_convenience_functions()
    success3 = test_edge_cases()
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! WorkingLanguageDetector is ready for use.")
        return True
    else:
        print("\n❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
