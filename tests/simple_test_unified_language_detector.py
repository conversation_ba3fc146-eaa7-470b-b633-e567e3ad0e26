#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test script cho UnifiedLanguageDetector.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_basic_functionality():
    """Test basic functionality."""
    print("🧪 Testing UnifiedLanguageDetector basic functionality...")
    
    try:
        # Import the module
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        print("✅ Import successful")
        
        # Create detector
        detector = UnifiedLanguageDetector(use_external_libraries=False)
        print("✅ Detector created")
        
        # Test English detection
        english_text = "Hello world, this is an English text."
        lang, conf = detector.detect_language(english_text, return_confidence=True)
        print(f"✅ English detection: {lang} (confidence: {conf:.2f})")
        
        # Test Vietnamese detection
        vietnamese_text = "Xin chào thế giới, đây là văn bản tiếng Vi<PERSON>t."
        lang, conf = detector.detect_language(vietnamese_text, return_confidence=True)
        print(f"✅ Vietnamese detection: {lang} (confidence: {conf:.2f})")
        
        # Test simple detect method
        simple_result = detector.detect(english_text)
        print(f"✅ Simple detect: {simple_result}")
        
        # Test utility methods
        assert detector.is_english("Hello world"), "Should detect English"
        assert detector.is_vietnamese("Xin chào"), "Should detect Vietnamese"
        print("✅ Utility methods work")
        
        # Test stats
        stats = detector.get_stats()
        print(f"✅ Stats: {stats['supported_languages']} languages supported")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing convenience functions...")
    
    try:
        from deep_research_core.utils.unified_language_detector import (
            detect_language, is_vietnamese, is_english, get_language_detector
        )
        
        # Test detect_language function
        lang, conf = detect_language("Hello world")
        print(f"✅ detect_language function: {lang} (confidence: {conf:.2f})")
        
        # Test is_vietnamese function
        assert is_vietnamese("Xin chào"), "Should detect Vietnamese"
        print("✅ is_vietnamese function works")
        
        # Test is_english function
        assert is_english("Hello world"), "Should detect English"
        print("✅ is_english function works")
        
        # Test global detector
        global_detector = get_language_detector()
        print("✅ Global detector works")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Starting simple UnifiedLanguageDetector tests...\n")
    
    success1 = test_basic_functionality()
    success2 = test_convenience_functions()
    
    if success1 and success2:
        print("\n🎉 All tests passed! UnifiedLanguageDetector is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
