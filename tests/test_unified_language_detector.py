#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho UnifiedLanguageDetector.

Script này test tất cả tính năng của UnifiedLanguageDetector để đảm bảo
việc consolidation language detection implementations thành công.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from deep_research_core.utils.unified_language_detector import (
    UnifiedLanguageDetector, 
    detect_language, 
    is_vietnamese, 
    is_english,
    get_language_detector
)

def test_basic_language_detection():
    """Test basic language detection functionality."""
    print("🧪 Testing basic language detection...")
    
    detector = UnifiedLanguageDetector()
    
    # Test English
    english_text = "This is a sample text in English language. It contains common English words."
    lang, conf = detector.detect_language(english_text, return_confidence=True)
    assert lang == "en", f"Expected 'en', got {lang}"
    assert conf > 0.5, f"Expected confidence > 0.5, got {conf}"
    print("✅ English detection works")
    
    # Test Vietnamese
    vietnamese_text = "<PERSON><PERSON><PERSON> là một đoạn văn bản tiếng Việt. Nó chứa các từ phổ biến trong tiếng Việt."
    lang, conf = detector.detect_language(vietnamese_text, return_confidence=True)
    assert lang == "vi", f"Expected 'vi', got {lang}"
    assert conf > 0.5, f"Expected confidence > 0.5, got {conf}"
    print("✅ Vietnamese detection works")
    
    # Test simple detection method
    simple_lang = detector.detect(english_text)
    assert simple_lang == "en", f"Expected 'en', got {simple_lang}"
    print("✅ Simple detect method works")

def test_confidence_and_fallback():
    """Test confidence scoring and fallback mechanisms."""
    print("\n🧪 Testing confidence and fallback...")
    
    detector = UnifiedLanguageDetector(min_confidence=0.8)
    
    # Test short text (should fallback to default)
    short_text = "Hi"
    lang, conf = detector.detect_language(short_text, return_confidence=True)
    assert lang == "en", f"Expected fallback to 'en', got {lang}"
    print("✅ Short text fallback works")
    
    # Test empty text
    empty_text = ""
    lang, conf = detector.detect_language(empty_text, return_confidence=True)
    assert lang == "en", f"Expected fallback to 'en', got {lang}"
    print("✅ Empty text fallback works")
    
    # Test mixed language text (should have lower confidence)
    mixed_text = "Hello xin chào world thế giới"
    lang, conf = detector.detect_language(mixed_text, return_confidence=True)
    print(f"✅ Mixed text detection: {lang} (confidence: {conf:.2f})")

def test_utility_methods():
    """Test utility methods."""
    print("\n🧪 Testing utility methods...")
    
    detector = UnifiedLanguageDetector()
    
    # Test is_vietnamese
    vietnamese_text = "Xin chào thế giới"
    assert detector.is_vietnamese(vietnamese_text), "Should detect Vietnamese"
    print("✅ is_vietnamese works")
    
    # Test is_english
    english_text = "Hello world"
    assert detector.is_english(english_text), "Should detect English"
    print("✅ is_english works")
    
    # Test is_language
    assert detector.is_language(vietnamese_text, "vi"), "Should detect Vietnamese"
    assert not detector.is_language(vietnamese_text, "en"), "Should not detect English"
    print("✅ is_language works")
    
    # Test get_supported_languages
    supported = detector.get_supported_languages()
    assert isinstance(supported, dict), "Should return dict"
    assert "en" in supported, "Should support English"
    assert "vi" in supported, "Should support Vietnamese"
    print(f"✅ Supported languages: {len(supported)} languages")

def test_caching():
    """Test caching functionality."""
    print("\n🧪 Testing caching...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        detector = UnifiedLanguageDetector(
            enable_cache=True,
            cache_dir=temp_dir,
            max_cache_size=100
        )
        
        text = "This is a test text for caching functionality."
        
        # First detection (should cache)
        lang1, conf1 = detector.detect_language(text, return_confidence=True)
        
        # Second detection (should use cache)
        lang2, conf2 = detector.detect_language(text, return_confidence=True)
        
        assert lang1 == lang2, "Cached result should be same"
        assert conf1 == conf2, "Cached confidence should be same"
        
        # Check cache stats
        stats = detector.get_stats()
        assert stats["cache_enabled"], "Cache should be enabled"
        assert stats["cache_size"] > 0, "Cache should have entries"
        print("✅ Caching works")

def test_multiple_languages():
    """Test detection of multiple languages."""
    print("\n🧪 Testing multiple languages...")
    
    detector = UnifiedLanguageDetector()
    
    test_texts = {
        "fr": "Bonjour le monde, comment allez-vous aujourd'hui?",
        "de": "Hallo Welt, wie geht es Ihnen heute?",
        "es": "Hola mundo, ¿cómo estás hoy?",
        "it": "Ciao mondo, come stai oggi?",
        "pt": "Olá mundo, como você está hoje?",
    }
    
    detected_correctly = 0
    for expected_lang, text in test_texts.items():
        if expected_lang in detector.supported_languages:
            lang, conf = detector.detect_language(text, return_confidence=True)
            if lang == expected_lang:
                detected_correctly += 1
            print(f"  {expected_lang}: {lang} (confidence: {conf:.2f})")
    
    print(f"✅ Detected {detected_correctly}/{len(test_texts)} languages correctly")

def test_augment_with_language_info():
    """Test data augmentation with language info."""
    print("\n🧪 Testing data augmentation...")
    
    detector = UnifiedLanguageDetector()
    
    # Test simple data
    data = {
        "title": "Hello World",
        "content": "This is an English text with some content.",
        "number": 42
    }
    
    augmented = detector.augment_with_language_info(data)
    assert "metadata" in augmented, "Should add metadata"
    assert "language" in augmented["metadata"], "Should add language info"
    print("✅ Simple data augmentation works")
    
    # Test nested results
    data_with_results = {
        "results": [
            {
                "title": "English Title",
                "snippet": "This is an English snippet"
            },
            {
                "title": "Tiêu đề tiếng Việt",
                "snippet": "Đây là một đoạn trích tiếng Việt"
            }
        ]
    }
    
    augmented = detector.augment_with_language_info(data_with_results)
    for result in augmented["results"]:
        if "metadata" in result and "language" in result["metadata"]:
            lang_info = result["metadata"]["language"]
            assert "code" in lang_info, "Should have language code"
            assert "confidence" in lang_info, "Should have confidence"
            assert "name" in lang_info, "Should have language name"
    
    print("✅ Nested results augmentation works")

def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing convenience functions...")
    
    # Test detect_language function
    lang, conf = detect_language("Hello world")
    assert lang == "en", f"Expected 'en', got {lang}"
    print("✅ detect_language function works")
    
    # Test is_vietnamese function
    assert is_vietnamese("Xin chào"), "Should detect Vietnamese"
    assert not is_vietnamese("Hello"), "Should not detect Vietnamese for English"
    print("✅ is_vietnamese function works")
    
    # Test is_english function
    assert is_english("Hello world"), "Should detect English"
    assert not is_english("Xin chào"), "Should not detect English for Vietnamese"
    print("✅ is_english function works")
    
    # Test global detector
    global_detector = get_language_detector()
    assert isinstance(global_detector, UnifiedLanguageDetector), "Should return detector instance"
    
    # Test that it returns the same instance
    global_detector2 = get_language_detector()
    assert global_detector is global_detector2, "Should return same instance"
    print("✅ Global detector works")

def test_external_libraries_fallback():
    """Test fallback when external libraries are not available."""
    print("\n🧪 Testing external libraries fallback...")
    
    # Create detector without external libraries
    detector = UnifiedLanguageDetector(use_external_libraries=False)
    
    # Should still work with built-in methods
    lang, conf = detector.detect_language("Hello world", return_confidence=True)
    assert lang in detector.supported_languages, f"Should detect supported language, got {lang}"
    
    stats = detector.get_stats()
    assert not stats["external_libraries"]["langdetect"], "langdetect should be disabled"
    assert not stats["external_libraries"]["langid"], "langid should be disabled"
    assert not stats["external_libraries"]["fasttext"], "fasttext should be disabled"
    
    print("✅ External libraries fallback works")

def test_error_handling():
    """Test error handling."""
    print("\n🧪 Testing error handling...")
    
    detector = UnifiedLanguageDetector()
    
    # Test with None input
    try:
        lang = detector.detect(None)
        assert lang == detector.default_language, "Should fallback to default"
        print("✅ None input handled gracefully")
    except Exception as e:
        print(f"⚠️ None input caused error: {e}")
    
    # Test with very long text
    very_long_text = "Hello world. " * 10000
    lang, conf = detector.detect_language(very_long_text, return_confidence=True)
    assert lang in detector.supported_languages, "Should handle long text"
    print("✅ Long text handled gracefully")
    
    # Test with special characters
    special_text = "Hello 🌍 world! @#$%^&*()"
    lang, conf = detector.detect_language(special_text, return_confidence=True)
    assert lang in detector.supported_languages, "Should handle special characters"
    print("✅ Special characters handled gracefully")

def run_all_tests():
    """Chạy tất cả tests."""
    print("🚀 Starting UnifiedLanguageDetector tests...\n")
    
    try:
        test_basic_language_detection()
        test_confidence_and_fallback()
        test_utility_methods()
        test_caching()
        test_multiple_languages()
        test_augment_with_language_info()
        test_convenience_functions()
        test_external_libraries_fallback()
        test_error_handling()
        
        print("\n🎉 All tests passed! UnifiedLanguageDetector is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
