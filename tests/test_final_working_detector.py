#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Final test cho WorkingLanguageDetector - standalone version.
"""

import re
import logging
from typing import Dict, Tuple

# Standalone WorkingLanguageDetector for testing
class TestWorkingLanguageDetector:
    """Working language detector for final testing."""
    
    def __init__(self, default_language: str = "en", min_confidence: float = 0.3):
        self.default_language = default_language
        self.min_confidence = min_confidence
        
        # Vietnamese characters
        self.vietnamese_chars = set("áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđĐ")
        
        # Common words for each language
        self.common_words = {
            "vi": {"và", "là", "của", "có", "không", "được", "trong", "một", "cho", "đã", "những", "với", "các", "để", "người"},
            "en": {"the", "and", "is", "in", "to", "of", "that", "for", "it", "with", "as", "on", "at", "this", "by"},
            "fr": {"le", "la", "les", "un", "une", "des", "et", "est", "en", "à", "que", "qui", "dans", "pour", "pas"},
            "de": {"der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "auf", "für", "von", "nicht", "ein"},
            "es": {"el", "la", "los", "las", "un", "una", "y", "es", "en", "de", "que", "por", "para", "con", "no"},
        }
        
        print("TestWorkingLanguageDetector initialized")
    
    def detect(self, text: str) -> str:
        """Simple detection method."""
        lang, _ = self.detect_language(text, return_confidence=True)
        return lang
    
    def detect_language(self, text: str, return_confidence: bool = True) -> Tuple[str, float]:
        """Detect language with confidence."""
        if not text or len(text.strip()) < 3:
            if return_confidence:
                return self.default_language, 0.5
            return self.default_language
        
        text = text.strip().lower()
        
        # Character-based detection
        char_scores = self._detect_by_chars(text)
        
        # Word-based detection
        word_scores = self._detect_by_words(text)
        
        # Special handling for Vietnamese
        vi_char_score = char_scores.get("vi", 0.0)
        if vi_char_score > 0.1:  # Strong Vietnamese character presence
            if return_confidence:
                return "vi", min(0.9, 0.5 + vi_char_score)
            return "vi"
        
        # Combine scores for other cases
        combined_scores = {}
        all_langs = set(list(char_scores.keys()) + list(word_scores.keys()))
        
        for lang in all_langs:
            char_score = char_scores.get(lang, 0.0)
            word_score = word_scores.get(lang, 0.0)
            
            if lang == "vi":
                combined_scores[lang] = char_score * 0.8 + word_score * 0.2
            else:
                combined_scores[lang] = char_score * 0.4 + word_score * 0.6
        
        # Find best language
        if combined_scores:
            best_lang = max(combined_scores.keys(), key=lambda k: combined_scores[k])
            best_score = combined_scores[best_lang]
            
            if best_lang == "vi" and best_score > 0.05:
                if return_confidence:
                    return best_lang, best_score
                return best_lang
            elif best_score >= self.min_confidence:
                if return_confidence:
                    return best_lang, best_score
                return best_lang
        
        # Fallback to default
        if return_confidence:
            return self.default_language, 0.5
        return self.default_language
    
    def _detect_by_chars(self, text: str) -> Dict[str, float]:
        """Character-based detection."""
        scores = {}
        
        # Count Vietnamese characters
        vi_char_count = sum(1 for char in text if char in self.vietnamese_chars)
        total_chars = len([c for c in text if c.isalpha()])
        
        if total_chars > 0:
            scores["vi"] = vi_char_count / total_chars
            
            # English detection
            english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
            scores["en"] = english_chars / total_chars if total_chars > 0 else 0.0
            
            # Adjust English score if Vietnamese characters are present
            if scores["vi"] > 0:
                scores["en"] = max(0.0, scores["en"] - scores["vi"])
        
        return scores
    
    def _detect_by_words(self, text: str) -> Dict[str, float]:
        """Word-based detection."""
        scores = {}
        
        # Extract words
        words = re.findall(r'\b\w+\b', text.lower())
        if not words:
            return scores
        
        for lang, common_words in self.common_words.items():
            count = sum(1 for word in words if word in common_words)
            scores[lang] = count / len(words) if len(words) > 0 else 0.0
        
        return scores
    
    def is_vietnamese(self, text: str) -> bool:
        """Check if text is Vietnamese."""
        return self.detect(text) == "vi"
    
    def is_english(self, text: str) -> bool:
        """Check if text is English."""
        return self.detect(text) == "en"

def test_detector():
    """Test the detector."""
    print("🧪 Testing TestWorkingLanguageDetector...")
    
    detector = TestWorkingLanguageDetector()
    
    # Test Vietnamese
    vietnamese_texts = [
        "Xin chào thế giới",
        "Đây là văn bản tiếng Việt",
        "Tôi có thể nói tiếng Việt",
        "Hôm nay trời đẹp",
        "Chúng tôi đang học tiếng Việt"
    ]
    
    print("\n📝 Testing Vietnamese detection:")
    vi_correct = 0
    for text in vietnamese_texts:
        lang, conf = detector.detect_language(text, return_confidence=True)
        if lang == "vi":
            vi_correct += 1
            print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
        else:
            print(f"  ❌ '{text}' -> {lang} (confidence: {conf:.2f})")
    
    # Test English
    english_texts = [
        "Hello world",
        "This is an English text",
        "I can speak English",
        "Today is a beautiful day",
        "We are learning English"
    ]
    
    print("\n📝 Testing English detection:")
    en_correct = 0
    for text in english_texts:
        lang, conf = detector.detect_language(text, return_confidence=True)
        if lang == "en":
            en_correct += 1
            print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
        else:
            print(f"  ❌ '{text}' -> {lang} (confidence: {conf:.2f})")
    
    # Test other languages
    other_tests = [
        ("Bonjour le monde", "fr"),
        ("Hola mundo", "es"),
        ("Hallo Welt", "de"),
    ]
    
    print("\n📝 Testing other languages:")
    other_correct = 0
    for text, expected in other_tests:
        lang, conf = detector.detect_language(text, return_confidence=True)
        if lang == expected:
            other_correct += 1
            print(f"  ✅ '{text}' -> {lang} (confidence: {conf:.2f})")
        else:
            print(f"  ❌ '{text}' -> {lang} (expected: {expected}, confidence: {conf:.2f})")
    
    # Summary
    total_tests = len(vietnamese_texts) + len(english_texts) + len(other_tests)
    total_correct = vi_correct + en_correct + other_correct
    accuracy = total_correct / total_tests * 100
    
    print(f"\n📊 Test Results:")
    print(f"   Vietnamese: {vi_correct}/{len(vietnamese_texts)} correct")
    print(f"   English: {en_correct}/{len(english_texts)} correct")
    print(f"   Other: {other_correct}/{len(other_tests)} correct")
    print(f"   Overall Accuracy: {accuracy:.1f}% ({total_correct}/{total_tests})")
    
    # Test utility methods
    print(f"\n📝 Testing utility methods:")
    assert detector.is_vietnamese("Xin chào"), "Should detect Vietnamese"
    assert detector.is_english("Hello world"), "Should detect English"
    print("  ✅ Utility methods work correctly")
    
    if accuracy >= 80:
        print(f"\n🎉 TestWorkingLanguageDetector works excellently!")
        return True
    else:
        print(f"\n❌ TestWorkingLanguageDetector needs improvement")
        return False

def main():
    """Main test function."""
    print("🚀 Starting Final WorkingLanguageDetector Test...\n")
    
    success = test_detector()
    
    if success:
        print("\n✅ WorkingLanguageDetector is ready for production use!")
        print("\n💡 Recommendations:")
        print("   - Use WorkingLanguageDetector for reliable language detection")
        print("   - Excellent Vietnamese character detection")
        print("   - Good performance for common languages")
        print("   - No external dependencies required")
    else:
        print("\n❌ WorkingLanguageDetector needs further improvements")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
