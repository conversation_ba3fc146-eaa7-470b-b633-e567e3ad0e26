#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Minimal test cho UnifiedLanguageDetector.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_minimal():
    """Minimal test."""
    print("🧪 Minimal test...")
    
    try:
        from deep_research_core.utils.unified_language_detector import UnifiedLanguageDetector
        print("✅ Import OK")
        
        detector = UnifiedLanguageDetector(use_external_libraries=False, enable_cache=False)
        print("✅ Detector created")
        
        # Test simple detection
        result = detector.detect("Hello world")
        print(f"✅ Simple detect: {result}")
        
        # Test Vietnamese
        result = detector.detect("Xin chào")
        print(f"✅ Vietnamese detect: {result}")
        
        # Test with confidence
        lang, conf = detector.detect_language("Xin chào thế giới", return_confidence=True)
        print(f"✅ Vietnamese with confidence: {lang} ({conf:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_minimal()
    sys.exit(0 if success else 1)
