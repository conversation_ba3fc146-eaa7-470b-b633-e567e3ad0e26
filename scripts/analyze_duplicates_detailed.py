#!/usr/bin/env python3
"""
Script để phân tích chi tiết các file trùng lặp giữa src/deep_research_core và advanced_reasoning_engine.
"""

import os
import json
import hashlib
import difflib
from pathlib import Path
from typing import Dict, List, Tuple, Set
import ast
import re

def calculate_file_hash(file_path: str) -> str:
    """Tính hash của file để so sánh."""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except Exception:
        return ""

def extract_functions_and_classes(file_path: str) -> Dict[str, List[str]]:
    """Tr<PERSON>ch xuất tên functions và classes từ Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        functions = []
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
            elif isinstance(node, ast.ClassDef):
                classes.append(node.name)
        
        return {"functions": functions, "classes": classes}
    except Exception as e:
        return {"functions": [], "classes": [], "error": str(e)}

def calculate_similarity(file1: str, file2: str) -> float:
    """Tính độ tương tự giữa 2 files."""
    try:
        with open(file1, 'r', encoding='utf-8') as f1:
            content1 = f1.read()
        with open(file2, 'r', encoding='utf-8') as f2:
            content2 = f2.read()
        
        # Sử dụng difflib để tính similarity
        similarity = difflib.SequenceMatcher(None, content1, content2).ratio()
        return similarity
    except Exception:
        return 0.0

def find_duplicate_files():
    """Tìm các file trùng lặp."""
    base_dir = Path(".")
    src_dir = base_dir / "src" / "deep_research_core"
    advanced_dir = base_dir / "advanced_reasoning_engine" / "src" / "deep_research_core"
    deepresearch_dir = base_dir / "src" / "deep_research_core" / "advanced_reasoning" / "deepresearch_organized" / "src" / "deep_research_core"
    
    # Tìm tất cả Python files
    src_files = list(src_dir.rglob("*.py")) if src_dir.exists() else []
    advanced_files = list(advanced_dir.rglob("*.py")) if advanced_dir.exists() else []
    deepresearch_files = list(deepresearch_dir.rglob("*.py")) if deepresearch_dir.exists() else []
    
    # Tạo mapping filename -> full paths
    file_mapping = {}
    
    for file_path in src_files + advanced_files + deepresearch_files:
        filename = file_path.name
        if filename not in file_mapping:
            file_mapping[filename] = []
        file_mapping[filename].append(str(file_path))
    
    # Tìm duplicates
    duplicates = {}
    for filename, paths in file_mapping.items():
        if len(paths) > 1:
            duplicates[filename] = paths
    
    return duplicates

def analyze_duplicates():
    """Phân tích chi tiết các file trùng lặp."""
    duplicates = find_duplicate_files()
    analysis_results = {}
    
    print("🔍 PHÂN TÍCH CHI TIẾT CÁC FILE TRÙNG LẶP")
    print("=" * 60)
    
    for filename, paths in duplicates.items():
        print(f"\n📁 File: {filename}")
        print(f"   Số lượng duplicates: {len(paths)}")
        
        file_analysis = {
            "paths": paths,
            "hashes": {},
            "similarities": {},
            "functions_classes": {},
            "identical_pairs": [],
            "similar_pairs": []
        }
        
        # Tính hash cho mỗi file
        for path in paths:
            file_hash = calculate_file_hash(path)
            file_analysis["hashes"][path] = file_hash
            
            # Trích xuất functions và classes
            symbols = extract_functions_and_classes(path)
            file_analysis["functions_classes"][path] = symbols
            
            print(f"   📄 {path}")
            print(f"      Hash: {file_hash[:8]}...")
            print(f"      Functions: {len(symbols['functions'])}")
            print(f"      Classes: {len(symbols['classes'])}")
        
        # So sánh từng cặp
        for i, path1 in enumerate(paths):
            for j, path2 in enumerate(paths[i+1:], i+1):
                similarity = calculate_similarity(path1, path2)
                pair_key = f"{path1} <-> {path2}"
                file_analysis["similarities"][pair_key] = similarity
                
                if similarity == 1.0:
                    file_analysis["identical_pairs"].append((path1, path2))
                    print(f"   🟢 IDENTICAL: {Path(path1).parent.name} <-> {Path(path2).parent.name}")
                elif similarity > 0.8:
                    file_analysis["similar_pairs"].append((path1, path2, similarity))
                    print(f"   🟡 SIMILAR ({similarity:.2%}): {Path(path1).parent.name} <-> {Path(path2).parent.name}")
                else:
                    print(f"   🔴 DIFFERENT ({similarity:.2%}): {Path(path1).parent.name} <-> {Path(path2).parent.name}")
        
        analysis_results[filename] = file_analysis
    
    return analysis_results

def generate_summary_report(analysis_results: Dict):
    """Tạo báo cáo tổng kết."""
    print("\n" + "=" * 60)
    print("📊 BÁO CÁO TỔNG KẾT")
    print("=" * 60)
    
    total_duplicates = len(analysis_results)
    identical_count = 0
    similar_count = 0
    different_count = 0
    
    high_priority_files = []
    medium_priority_files = []
    low_priority_files = []
    
    for filename, analysis in analysis_results.items():
        if analysis["identical_pairs"]:
            identical_count += 1
            high_priority_files.append(filename)
        elif analysis["similar_pairs"]:
            similar_count += 1
            medium_priority_files.append(filename)
        else:
            different_count += 1
            low_priority_files.append(filename)
    
    print(f"📈 Tổng số files trùng lặp: {total_duplicates}")
    print(f"🟢 Files hoàn toàn giống nhau: {identical_count}")
    print(f"🟡 Files tương tự (>80%): {similar_count}")
    print(f"🔴 Files khác nhau (<80%): {different_count}")
    
    print(f"\n🔥 ƯU TIÊN CAO (Merge ngay): {len(high_priority_files)} files")
    for filename in high_priority_files:
        print(f"   - {filename}")
    
    print(f"\n⚡ ƯU TIÊN TRUNG BÌNH: {len(medium_priority_files)} files")
    for filename in medium_priority_files:
        print(f"   - {filename}")
    
    print(f"\n📋 ƯU TIÊN THẤP: {len(low_priority_files)} files")
    for filename in low_priority_files:
        print(f"   - {filename}")
    
    # Lưu kết quả vào file JSON
    output_file = "reports/detailed_duplicate_analysis.json"
    os.makedirs("reports", exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "summary": {
                "total_duplicates": total_duplicates,
                "identical_count": identical_count,
                "similar_count": similar_count,
                "different_count": different_count,
                "high_priority_files": high_priority_files,
                "medium_priority_files": medium_priority_files,
                "low_priority_files": low_priority_files
            },
            "detailed_analysis": analysis_results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Báo cáo chi tiết đã được lưu vào: {output_file}")

def main():
    """Main function."""
    print("🚀 BẮT ĐẦU PHÂN TÍCH DUPLICATES...")
    
    analysis_results = analyze_duplicates()
    generate_summary_report(analysis_results)
    
    print("\n✅ HOÀN THÀNH PHÂN TÍCH!")

if __name__ == "__main__":
    main()
